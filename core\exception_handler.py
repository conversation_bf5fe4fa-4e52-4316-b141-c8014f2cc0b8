"""
异常处理与用户体验优化模块
增强错误处理机制，添加用户友好的提示，改进日志系统
"""
import sys
import traceback
import logging
from typing import Optional, Callable, Dict, Any, Type
from functools import wraps
from dataclasses import dataclass
from enum import Enum
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtWidgets import QApplication, QMessageBox
from qfluentwidgets import InfoBar, InfoBarPosition, MessageBox

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """错误严重程度"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ErrorInfo:
    """错误信息"""
    error_type: str
    message: str
    details: str
    severity: ErrorSeverity
    component: str
    timestamp: str
    user_message: str
    suggested_action: str = ""


class ExceptionHandler(QObject):
    """异常处理器"""
    
    error_occurred = pyqtSignal(ErrorInfo)
    
    def __init__(self):
        super().__init__()
        self._error_handlers: Dict[Type[Exception], Callable] = {}
        self._default_handler: Optional[Callable] = None
        self._user_friendly_messages = {
            'ConnectionError': '网络连接失败，请检查网络设置',
            'TimeoutError': '操作超时，请稍后重试',
            'FileNotFoundError': '文件未找到，请检查文件路径',
            'PermissionError': '权限不足，请以管理员身份运行',
            'ValueError': '参数值错误，请检查输入',
            'RuntimeError': '运行时错误，请重启应用程序',
            'ImportError': '模块导入失败，请检查依赖库',
            'AttributeError': '属性错误，可能是版本兼容问题',
            'KeyError': '配置项缺失，请检查配置文件',
            'TypeError': '类型错误，请检查数据格式'
        }
        
    def register_handler(self, exception_type: Type[Exception], handler: Callable):
        """注册异常处理器"""
        self._error_handlers[exception_type] = handler
        logger.debug(f"已注册异常处理器：{exception_type.__name__}")
        
    def set_default_handler(self, handler: Callable):
        """设置默认异常处理器"""
        self._default_handler = handler
        
    def handle_exception(self, 
                        exception: Exception, 
                        component: str = "Unknown",
                        user_message: str = "",
                        suggested_action: str = "") -> ErrorInfo:
        """处理异常"""
        
        # 获取异常信息
        error_type = type(exception).__name__
        message = str(exception)
        details = traceback.format_exc()
        
        # 确定严重程度
        severity = self._determine_severity(exception)
        
        # 生成用户友好消息
        if not user_message:
            user_message = self._get_user_friendly_message(exception)
            
        # 创建错误信息
        error_info = ErrorInfo(
            error_type=error_type,
            message=message,
            details=details,
            severity=severity,
            component=component,
            timestamp=self._get_timestamp(),
            user_message=user_message,
            suggested_action=suggested_action
        )
        
        # 记录日志
        self._log_error(error_info)
        
        # 调用特定处理器
        if type(exception) in self._error_handlers:
            try:
                self._error_handlers[type(exception)](error_info)
            except Exception as e:
                logger.error(f"异常处理器执行失败：{e}")
                
        # 调用默认处理器
        elif self._default_handler:
            try:
                self._default_handler(error_info)
            except Exception as e:
                logger.error(f"默认异常处理器执行失败：{e}")
                
        # 发送信号
        self.error_occurred.emit(error_info)
        
        return error_info
        
    def _determine_severity(self, exception: Exception) -> ErrorSeverity:
        """确定错误严重程度"""
        critical_exceptions = (SystemExit, KeyboardInterrupt, MemoryError)
        error_exceptions = (RuntimeError, ConnectionError, TimeoutError)
        warning_exceptions = (UserWarning, DeprecationWarning)
        
        if isinstance(exception, critical_exceptions):
            return ErrorSeverity.CRITICAL
        elif isinstance(exception, error_exceptions):
            return ErrorSeverity.ERROR
        elif isinstance(exception, warning_exceptions):
            return ErrorSeverity.WARNING
        else:
            return ErrorSeverity.ERROR
            
    def _get_user_friendly_message(self, exception: Exception) -> str:
        """获取用户友好的错误消息"""
        error_type = type(exception).__name__
        return self._user_friendly_messages.get(error_type, "发生未知错误，请联系技术支持")
        
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
    def _log_error(self, error_info: ErrorInfo):
        """记录错误日志"""
        log_message = f"[{error_info.component}] {error_info.error_type}: {error_info.message}"
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
            logger.critical(f"详细信息：\n{error_info.details}")
        elif error_info.severity == ErrorSeverity.ERROR:
            logger.error(log_message)
            logger.debug(f"详细信息：\n{error_info.details}")
        elif error_info.severity == ErrorSeverity.WARNING:
            logger.warning(log_message)
        else:
            logger.info(log_message)


class UserNotificationManager(QObject):
    """用户通知管理器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_widget = parent
        
    def show_error_notification(self, error_info: ErrorInfo):
        """显示错误通知"""
        try:
            if error_info.severity == ErrorSeverity.CRITICAL:
                self._show_critical_error_dialog(error_info)
            elif error_info.severity == ErrorSeverity.ERROR:
                self._show_error_info_bar(error_info)
            elif error_info.severity == ErrorSeverity.WARNING:
                self._show_warning_info_bar(error_info)
            else:
                self._show_info_info_bar(error_info)
                
        except Exception as e:
            logger.error(f"显示用户通知失败：{e}")
            
    def _show_critical_error_dialog(self, error_info: ErrorInfo):
        """显示严重错误对话框"""
        dialog = MessageBox(
            title="严重错误",
            content=f"{error_info.user_message}\n\n{error_info.suggested_action}",
            parent=self.parent_widget
        )
        dialog.exec()
        
    def _show_error_info_bar(self, error_info: ErrorInfo):
        """显示错误信息条"""
        try:
            InfoBar.error(
                title="错误",
                content=error_info.user_message,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=5000,
                parent=self.parent_widget
            )
        except Exception as e:
            # 如果InfoBar失败，使用简单的消息框
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self.parent_widget, "错误", error_info.user_message)

    def _show_warning_info_bar(self, error_info: ErrorInfo):
        """显示警告信息条"""
        try:
            InfoBar.warning(
                title="警告",
                content=error_info.user_message,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self.parent_widget
            )
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self.parent_widget, "警告", error_info.user_message)

    def _show_info_info_bar(self, error_info: ErrorInfo):
        """显示信息条"""
        try:
            InfoBar.info(
                title="提示",
                content=error_info.user_message,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self.parent_widget
            )
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self.parent_widget, "提示", error_info.user_message)

    def show_success_notification(self, title: str, message: str):
        """显示成功通知"""
        try:
            InfoBar.success(
                title=title,
                content=message,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self.parent_widget
            )
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self.parent_widget, title, message)


def exception_handler(component: str = "", 
                     user_message: str = "",
                     suggested_action: str = "",
                     reraise: bool = False):
    """异常处理装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 获取全局异常处理器
                handler = getattr(QApplication.instance(), '_exception_handler', None)
                if handler:
                    handler.handle_exception(
                        e, 
                        component or func.__name__,
                        user_message,
                        suggested_action
                    )
                else:
                    logger.error(f"未找到异常处理器，直接记录错误：{e}")
                    
                if reraise:
                    raise
                    
        return wrapper
    return decorator


def safe_execute(func: Callable, 
                *args, 
                component: str = "",
                user_message: str = "",
                default_return=None,
                **kwargs):
    """安全执行函数"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        handler = getattr(QApplication.instance(), '_exception_handler', None)
        if handler:
            handler.handle_exception(
                e,
                component or func.__name__,
                user_message
            )
        else:
            logger.error(f"安全执行失败：{e}")
            
        return default_return


class GlobalExceptionHandler:
    """全局异常处理器"""
    
    def __init__(self, app: QApplication):
        self.app = app
        self.exception_handler = ExceptionHandler()
        self.notification_manager = UserNotificationManager()
        
        # 设置全局异常处理
        sys.excepthook = self._handle_exception
        
        # 将异常处理器附加到应用程序
        app._exception_handler = self.exception_handler
        app._notification_manager = self.notification_manager
        
        # 连接信号
        self.exception_handler.error_occurred.connect(
            self.notification_manager.show_error_notification
        )
        
    def _handle_exception(self, exc_type, exc_value, exc_traceback):
        """处理未捕获的异常"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许 Ctrl+C 正常退出
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
            
        # 创建异常对象
        exception = exc_value if exc_value else exc_type()
        
        # 处理异常
        self.exception_handler.handle_exception(
            exception,
            component="Global",
            user_message="应用程序遇到未处理的错误",
            suggested_action="请重启应用程序，如果问题持续存在，请联系技术支持"
        )
        
        # 记录到标准错误输出
        sys.__excepthook__(exc_type, exc_value, exc_traceback)


def setup_global_exception_handling(app: QApplication) -> GlobalExceptionHandler:
    """设置全局异常处理"""
    return GlobalExceptionHandler(app)
