"""
UI响应性改进模块
确保界面在长时间任务中保持响应，优化事件处理
"""
import logging
from typing import Optional, Callable, Any, Dict
from PyQt5.QtCore import QObject, QTimer, pyqtSignal, QThread, QMutex, QMutexLocker
from PyQt5.QtWidgets import QApplication, QProgressBar, QLabel, QWidget, QVBoxLayout, QHBoxLayout
from qfluentwidgets import ProgressBar, BodyLabel, InfoBar, InfoBarPosition

logger = logging.getLogger(__name__)


class ProgressManager(QObject):
    """进度管理器，管理长时间任务的进度显示"""
    
    progress_updated = pyqtSignal(str, int, str)  # task_id, progress, message
    task_started = pyqtSignal(str, str)  # task_id, description
    task_completed = pyqtSignal(str, bool, str)  # task_id, success, message
    
    def __init__(self):
        super().__init__()
        self._tasks: Dict[str, Dict[str, Any]] = {}
        self._mutex = QMutex()
        
    def start_task(self, task_id: str, description: str, total_steps: int = 100):
        """开始任务"""
        with QMutexLocker(self._mutex):
            self._tasks[task_id] = {
                'description': description,
                'total_steps': total_steps,
                'current_step': 0,
                'is_running': True,
                'start_time': None
            }
            
        self.task_started.emit(task_id, description)
        logger.debug(f"任务开始：{task_id} - {description}")
        
    def update_progress(self, task_id: str, current_step: int, message: str = ""):
        """更新进度"""
        with QMutexLocker(self._mutex):
            if task_id in self._tasks and self._tasks[task_id]['is_running']:
                task = self._tasks[task_id]
                task['current_step'] = current_step
                
                # 计算百分比
                progress = min(100, int((current_step / task['total_steps']) * 100))
                
                self.progress_updated.emit(task_id, progress, message)
                
    def complete_task(self, task_id: str, success: bool = True, message: str = ""):
        """完成任务"""
        with QMutexLocker(self._mutex):
            if task_id in self._tasks:
                self._tasks[task_id]['is_running'] = False
                
        self.task_completed.emit(task_id, success, message)
        logger.debug(f"任务完成：{task_id} - 成功：{success}")
        
    def cancel_task(self, task_id: str):
        """取消任务"""
        with QMutexLocker(self._mutex):
            if task_id in self._tasks:
                self._tasks[task_id]['is_running'] = False
                
        self.task_completed.emit(task_id, False, "任务已取消")
        logger.debug(f"任务取消：{task_id}")
        
    def is_task_running(self, task_id: str) -> bool:
        """检查任务是否正在运行"""
        with QMutexLocker(self._mutex):
            return task_id in self._tasks and self._tasks[task_id]['is_running']
            
    def get_running_tasks(self) -> list:
        """获取正在运行的任务列表"""
        with QMutexLocker(self._mutex):
            return [
                task_id for task_id, task in self._tasks.items() 
                if task['is_running']
            ]


class ResponsiveProgressDialog(QWidget):
    """响应式进度对话框"""
    
    cancelled = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("处理中...")
        self.setFixedSize(400, 150)
        self._setup_ui()
        
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 任务描述
        self.description_label = BodyLabel("正在处理...", self)
        layout.addWidget(self.description_label)
        
        # 进度条
        self.progress_bar = ProgressBar(self)
        self.progress_bar.setRange(0, 100)
        layout.addWidget(self.progress_bar)
        
        # 状态信息
        self.status_label = BodyLabel("", self)
        layout.addWidget(self.status_label)
        
    def update_progress(self, progress: int, message: str = ""):
        """更新进度"""
        self.progress_bar.setValue(progress)
        if message:
            self.status_label.setText(message)
            
    def set_description(self, description: str):
        """设置任务描述"""
        self.description_label.setText(description)
        
    def closeEvent(self, event):
        """关闭事件"""
        self.cancelled.emit()
        super().closeEvent(event)


class UIResponsiveManager(QObject):
    """UI响应性管理器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.progress_manager = ProgressManager()
        self._active_dialogs: Dict[str, ResponsiveProgressDialog] = {}
        self._keep_alive_timer = QTimer()
        self._keep_alive_timer.timeout.connect(self._keep_ui_responsive)
        
        # 连接进度管理器信号
        self.progress_manager.task_started.connect(self._on_task_started)
        self.progress_manager.progress_updated.connect(self._on_progress_updated)
        self.progress_manager.task_completed.connect(self._on_task_completed)
        
    def start_keep_alive(self, interval_ms: int = 100):
        """启动保活定时器，确保UI响应"""
        self._keep_alive_timer.start(interval_ms)
        
    def stop_keep_alive(self):
        """停止保活定时器"""
        self._keep_alive_timer.stop()
        
    def _keep_ui_responsive(self):
        """保持UI响应"""
        QApplication.processEvents()
        
    def show_progress_dialog(self, task_id: str, description: str, parent=None) -> ResponsiveProgressDialog:
        """显示进度对话框"""
        if task_id in self._active_dialogs:
            return self._active_dialogs[task_id]
            
        dialog = ResponsiveProgressDialog(parent)
        dialog.set_description(description)
        dialog.cancelled.connect(lambda: self.progress_manager.cancel_task(task_id))
        
        self._active_dialogs[task_id] = dialog
        dialog.show()
        
        return dialog
        
    def hide_progress_dialog(self, task_id: str):
        """隐藏进度对话框"""
        if task_id in self._active_dialogs:
            dialog = self._active_dialogs.pop(task_id)
            dialog.close()
            
    def _on_task_started(self, task_id: str, description: str):
        """任务开始处理"""
        logger.debug(f"UI响应管理器：任务开始 {task_id}")
        
    def _on_progress_updated(self, task_id: str, progress: int, message: str):
        """进度更新处理"""
        if task_id in self._active_dialogs:
            self._active_dialogs[task_id].update_progress(progress, message)
            
    def _on_task_completed(self, task_id: str, success: bool, message: str):
        """任务完成处理"""
        self.hide_progress_dialog(task_id)
        logger.debug(f"UI响应管理器：任务完成 {task_id} - 成功：{success}")


class NonBlockingWorker(QThread):
    """非阻塞工作线程基类"""
    
    progress_updated = pyqtSignal(int, str)  # progress, message
    work_completed = pyqtSignal(bool, object)  # success, result
    error_occurred = pyqtSignal(str)  # error_message
    
    def __init__(self, work_func: Callable, *args, **kwargs):
        super().__init__()
        self.work_func = work_func
        self.args = args
        self.kwargs = kwargs
        self._cancelled = False
        
    def run(self):
        """运行工作函数"""
        try:
            # 如果工作函数支持进度回调，传入回调函数
            if 'progress_callback' in self.kwargs:
                self.kwargs['progress_callback'] = self._progress_callback
                
            result = self.work_func(*self.args, **self.kwargs)
            
            if not self._cancelled:
                self.work_completed.emit(True, result)
                
        except Exception as e:
            if not self._cancelled:
                self.error_occurred.emit(str(e))
                self.work_completed.emit(False, None)
                
    def _progress_callback(self, progress: int, message: str = ""):
        """进度回调"""
        if not self._cancelled:
            self.progress_updated.emit(progress, message)
            
    def cancel(self):
        """取消工作"""
        self._cancelled = True
        
    def is_cancelled(self) -> bool:
        """检查是否已取消"""
        return self._cancelled


class AsyncTaskManager(QObject):
    """异步任务管理器"""
    
    task_completed = pyqtSignal(str, bool, object)  # task_id, success, result
    
    def __init__(self, ui_manager: UIResponsiveManager):
        super().__init__()
        self.ui_manager = ui_manager
        self._workers: Dict[str, NonBlockingWorker] = {}
        
    def run_async_task(self, 
                      task_id: str, 
                      description: str,
                      work_func: Callable,
                      show_progress: bool = True,
                      parent=None,
                      *args, **kwargs) -> str:
        """运行异步任务"""
        
        # 如果任务已存在，先取消
        if task_id in self._workers:
            self.cancel_task(task_id)
            
        # 创建工作线程
        worker = NonBlockingWorker(work_func, *args, **kwargs)
        self._workers[task_id] = worker
        
        # 连接信号
        worker.progress_updated.connect(
            lambda p, m: self.ui_manager.progress_manager.update_progress(task_id, p, m)
        )
        worker.work_completed.connect(
            lambda success, result: self._on_task_completed(task_id, success, result)
        )
        worker.error_occurred.connect(
            lambda error: logger.error(f"任务 {task_id} 出错：{error}")
        )
        
        # 启动进度管理
        self.ui_manager.progress_manager.start_task(task_id, description)
        
        # 显示进度对话框
        if show_progress:
            self.ui_manager.show_progress_dialog(task_id, description, parent)
            
        # 启动工作线程
        worker.start()
        
        logger.info(f"异步任务已启动：{task_id} - {description}")
        return task_id
        
    def cancel_task(self, task_id: str):
        """取消任务"""
        if task_id in self._workers:
            worker = self._workers[task_id]
            worker.cancel()
            worker.wait(3000)  # 等待最多3秒
            
            if worker.isRunning():
                worker.terminate()
                worker.wait(1000)
                
            del self._workers[task_id]
            
        self.ui_manager.progress_manager.cancel_task(task_id)
        logger.info(f"任务已取消：{task_id}")
        
    def _on_task_completed(self, task_id: str, success: bool, result: Any):
        """任务完成处理"""
        # 清理工作线程
        if task_id in self._workers:
            del self._workers[task_id]
            
        # 完成进度管理
        message = "任务完成" if success else "任务失败"
        self.ui_manager.progress_manager.complete_task(task_id, success, message)
        
        # 发送完成信号
        self.task_completed.emit(task_id, success, result)
        
    def get_running_tasks(self) -> list:
        """获取正在运行的任务"""
        return list(self._workers.keys())
        
    def cancel_all_tasks(self):
        """取消所有任务"""
        task_ids = list(self._workers.keys())
        for task_id in task_ids:
            self.cancel_task(task_id)
