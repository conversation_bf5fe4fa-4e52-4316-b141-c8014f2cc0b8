"""
仅测试相机控制器的回调函数修复
"""
import sys
import logging
import numpy as np
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, QObject, pyqtSignal

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 模拟 gxipy 模块
class MockGxipy:
    class DeviceManager:
        def update_all_device_list(self):
            return 1, [{"sn": "MOCK_CAMERA_001"}]
            
        def open_device_by_sn(self, sn):
            return MockCamera()
            
        def create_image_format_convert(self):
            return MockImageFormatConvert()
            
        def create_image_process(self):
            return MockImageProcess()
    
    class GxPixelFormatEntry:
        GX_PIXEL_FORMAT_BAYER_RG8 = "BAYER_RG8"

class MockCamera:
    def __init__(self):
        self.data_stream = [MockDataStream()]
        
    def get_remote_device_feature_control(self):
        return MockRemoteDeviceFeature()
        
    def create_image_process_config(self):
        return MockImageProcessConfig()
        
    def stream_on(self):
        print("✅ 模拟相机开始采集")
        
    def stream_off(self):
        print("✅ 模拟相机停止采集")
        
    def close_device(self):
        print("✅ 模拟相机设备关闭")

class MockDataStream:
    def __init__(self):
        self.callback = None
        
    def register_capture_callback(self, callback):
        # 这里模拟 gxipy 的检查逻辑
        if hasattr(callback, '__self__'):
            raise TypeError("DataStream.register_capture_callback: Expected callback type is function not <class 'method'>")
        self.callback = callback
        print("✅ 回调函数注册成功！")
        
    def unregister_capture_callback(self):
        self.callback = None
        print("✅ 回调函数取消注册成功")

class MockRemoteDeviceFeature:
    def get_enum_feature(self, name):
        return MockEnumFeature()
        
    def get_float_feature(self, name):
        return MockFloatFeature()

class MockEnumFeature:
    def set(self, value):
        print(f"✅ 设置枚举特性 {value}")
        
class MockFloatFeature:
    def set(self, value):
        print(f"✅ 设置浮点特性 {value}")
        
    def get(self):
        return 1.0

class MockImageProcessConfig:
    pass

class MockImageFormatConvert:
    pass

class MockImageProcess:
    pass

# 替换 gxipy 模块
sys.modules['gxipy'] = MockGxipy()

# 现在导入相机控制器
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtCore import QObject, pyqtSignal, QMutex, QMutexLocker
from typing import Optional
import weakref

class CameraController(QObject):
    """简化的相机控制器用于测试"""
    
    image_captured = pyqtSignal(np.ndarray)
    camera_error = pyqtSignal(str)
    camera_status_changed = pyqtSignal(bool)
    
    def __init__(self):
        super().__init__()
        self.cam = None
        self.device_manager = None
        self.remote_device_feature = None
        self._capture_callback = None
        self._is_initialized = False
        self._mutex = QMutex()
        
    def initialize(self):
        """初始化相机"""
        try:
            import gxipy as gx
            
            # 创建设备管理器
            self.device_manager = gx.DeviceManager()
            
            # 更新设备列表
            dev_num, dev_info_list = self.device_manager.update_all_device_list()
            if dev_num == 0:
                error_msg = "未找到相机设备"
                print(f"❌ {error_msg}")
                self.camera_error.emit(error_msg)
                return False
                
            # 打开第一个设备
            sn = dev_info_list[0].get("sn")
            print(f"✅ 找到相机设备，序列号：{sn}")
            self.cam = self.device_manager.open_device_by_sn(sn)
            
            # 获取设备特性控制器
            self.remote_device_feature = self.cam.get_remote_device_feature_control()
            
            # 注册回调函数 - 使用修复后的方法
            weak_self = weakref.ref(self)
            
            def capture_callback(raw_image):
                """独立的图像采集回调函数"""
                try:
                    self_ref = weak_self()
                    if self_ref is not None:
                        self_ref._on_image_captured(raw_image)
                except Exception as e:
                    print(f"❌ 图像采集回调处理失败：{e}")
                    
            self._capture_callback = capture_callback
            self.cam.data_stream[0].register_capture_callback(self._capture_callback)
            
            # 开始采集
            self.cam.stream_on()
            
            self._is_initialized = True
            self.camera_status_changed.emit(True)
            print("✅ 相机初始化完成")
            return True
            
        except Exception as e:
            error_msg = f"相机初始化失败：{e}"
            print(f"❌ {error_msg}")
            self.camera_error.emit(error_msg)
            return False
            
    def _on_image_captured(self, raw_image):
        """图像采集回调处理"""
        try:
            # 创建模拟图像
            mock_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            self.image_captured.emit(mock_image)
            print("✅ 图像处理完成")
        except Exception as e:
            print(f"❌ 图像处理失败：{e}")
            
    def close(self):
        """关闭相机"""
        try:
            if self.cam:
                # 取消注册回调函数
                if hasattr(self.cam, 'data_stream') and self._capture_callback:
                    try:
                        self.cam.data_stream[0].unregister_capture_callback()
                    except Exception as e:
                        print(f"⚠️ 取消注册回调函数失败：{e}")
                        
                # 停止流和关闭设备
                if hasattr(self.cam, 'stream_off'):
                    self.cam.stream_off()
                if hasattr(self.cam, 'close_device'):
                    self.cam.close_device()
                self.cam = None
                
            self._capture_callback = None
            self._is_initialized = False
            self.camera_status_changed.emit(False)
            print("✅ 相机资源清理完成")
            
        except Exception as e:
            print(f"❌ 清理相机资源时出错：{e}")

def test_camera_fix():
    """测试相机修复"""
    print("=== 测试相机回调函数修复 ===\n")
    
    app = QApplication(sys.argv)
    
    # 创建相机控制器
    camera = CameraController()
    
    # 连接信号
    def on_image_captured(image):
        print(f"✅ 图像采集成功，尺寸：{image.shape}")
        
    def on_camera_error(error):
        print(f"❌ 相机错误：{error}")
        
    def on_status_changed(status):
        print(f"📊 相机状态变化：{'在线' if status else '离线'}")
    
    camera.image_captured.connect(on_image_captured)
    camera.camera_error.connect(on_camera_error)
    camera.camera_status_changed.connect(on_status_changed)
    
    # 测试初始化
    print("🔧 正在初始化相机...")
    success = camera.initialize()
    
    if success:
        print("\n🎉 相机初始化成功！回调函数修复有效！")
        
        # 模拟图像采集
        print("\n📸 模拟图像采集...")
        if camera._capture_callback:
            # 创建模拟的原始图像数据
            class MockRawImage:
                pass
            mock_raw = MockRawImage()
            camera._capture_callback(mock_raw)
        
        # 延迟关闭
        QTimer.singleShot(1000, lambda: camera.close())
        QTimer.singleShot(1500, app.quit)
        
    else:
        print("\n❌ 相机初始化失败")
        QTimer.singleShot(500, app.quit)
    
    # 运行应用程序
    app.exec()
    
    print("\n=== 测试完成 ===")
    if success:
        print("✅ 回调函数修复验证成功！")
        print("✅ 现在可以正常运行主程序了")
    else:
        print("❌ 测试失败，需要进一步检查")

if __name__ == "__main__":
    test_camera_fix()
