"""
UI组件模块
包含自定义的UI组件
"""
import logging
from typing import Optional
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QImage
from PyQt5.QtWidgets import QLabel, QDialog, QVBoxLayout, QSizePolicy
from qfluentwidgets import MessageBoxBase, SubtitleLabel, BodyLabel, PasswordLineEdit
import numpy as np
import cv2

logger = logging.getLogger(__name__)


class ThreadSafeLogHandler(logging.Handler):
    """线程安全的日志处理器，将日志输出到TextBrowser组件"""
    
    def __init__(self, text_browser):
        super().__init__()
        self.text_browser = text_browser
        self.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        
    def emit(self, record):
        """发送日志记录"""
        try:
            if self.text_browser:
                msg = self.format(record)
                # 使用Qt的信号槽机制确保线程安全
                self.text_browser.append(msg)
                # 自动滚动到底部
                cursor = self.text_browser.textCursor()
                cursor.movePosition(cursor.End)
                self.text_browser.setTextCursor(cursor)
        except Exception as e:
            # 避免在日志处理器中再次记录日志导致递归
            print(f"日志处理器错误：{e}")


class ClickableImageLabel(QLabel):
    """可点击的图像标签"""
    
    clicked = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setCursor(Qt.PointingHandCursor)
        self.setAlignment(Qt.AlignCenter)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setMinimumSize(1, 1)
        self.setStyleSheet("""
            QLabel {
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: #f9f9f9;
            }
            QLabel:hover {
                border-color: #0078d4;
            }
        """)
        
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)
        
    def set_image(self, image: np.ndarray):
        """
        设置显示的图像
        Args:
            image: numpy图像数组
        """
        try:
            if image is None:
                self.clear()
                return
                
            # 转换为QImage
            if len(image.shape) == 2:
                # 灰度图像
                height, width = image.shape
                qimg = QImage(image.data, width, height, width, QImage.Format_Grayscale8)
            else:
                # 彩色图像
                height, width, channels = image.shape
                if channels == 3:
                    qimg = QImage(image.data, width, height, width * 3, QImage.Format_RGB888)
                elif channels == 4:
                    qimg = QImage(image.data, width, height, width * 4, QImage.Format_RGBA8888)
                else:
                    logger.warning(f"不支持的图像通道数：{channels}")
                    return
                    
            # 转换为QPixmap并设置
            pixmap = QPixmap.fromImage(qimg)
            self.setPixmap(pixmap.scaled(self.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation))
            
        except Exception as e:
            logger.error(f"设置图像失败：{e}")
            
    def get_pixmap(self) -> Optional[QPixmap]:
        """获取当前显示的QPixmap"""
        return self.pixmap()


class FullscreenImageViewer(QDialog):
    """全屏图像查看器"""
    
    def __init__(self, pixmap: QPixmap, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.Window)
        self.setWindowTitle("全屏预览")
        self.setWindowState(Qt.WindowFullScreen)
        
        # 创建图像标签
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("background-color: black;")
        
        # 设置图像
        if pixmap and not pixmap.isNull():
            scaled_pixmap = pixmap.scaled(
                self.size(), 
                Qt.KeepAspectRatio, 
                Qt.SmoothTransformation
            )
            self.image_label.setPixmap(scaled_pixmap)
        
        # 设置布局
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.image_label)
        self.setLayout(layout)
        
    def mousePressEvent(self, event):
        """鼠标点击关闭"""
        self.close()
        
    def keyPressEvent(self, event):
        """按键事件处理"""
        if event.key() in (Qt.Key_Escape, Qt.Key_Space, Qt.Key_Return):
            self.close()
        super().keyPressEvent(event)
        
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 重新缩放图像
        pixmap = self.image_label.pixmap()
        if pixmap and not pixmap.isNull():
            scaled_pixmap = pixmap.scaled(
                self.size(),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            self.image_label.setPixmap(scaled_pixmap)


class LoginDialog(MessageBoxBase):
    """登录对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.title = SubtitleLabel('身份验证', self)
        
        # 密码输入
        self.password_label = BodyLabel("请输入密码:", self)
        self.password_edit = PasswordLineEdit(self)
        self.password_edit.setPlaceholderText("请输入密码")
        
        # 布局
        self.viewLayout.addWidget(self.title)
        self.viewLayout.addWidget(self.password_label)
        self.viewLayout.addWidget(self.password_edit)
        
        # 按钮文本
        self.yesButton.setText("确定")
        self.cancelButton.setText("取消")
        
        # 设置最小宽度
        self.widget.setMinimumWidth(350)
        
        # 焦点设置
        self.password_edit.setFocus()
        
    def get_password(self) -> str:
        """获取输入的密码"""
        return self.password_edit.text()
        
    def clear_password(self):
        """清空密码输入"""
        self.password_edit.clear()


class ImageProcessor:
    """图像处理工具类"""
    
    @staticmethod
    def resize_image(image: np.ndarray, scale: float = 0.2) -> np.ndarray:
        """
        缩放图像
        Args:
            image: 输入图像
            scale: 缩放比例
        Returns:
            np.ndarray: 缩放后的图像
        """
        try:
            if image is None:
                return None
                
            height, width = image.shape[:2]
            new_width = int(width * scale)
            new_height = int(height * scale)
            
            resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            return resized
            
        except Exception as e:
            logger.error(f"图像缩放失败：{e}")
            return image
            
    @staticmethod
    def draw_detection_results(image: np.ndarray, draw_data: list) -> np.ndarray:
        """
        在图像上绘制检测结果
        Args:
            image: 输入图像
            draw_data: 绘制数据列表
        Returns:
            np.ndarray: 绘制后的图像
        """
        try:
            if image is None or not draw_data:
                return image
                
            result_image = image.copy()
            
            for item in draw_data:
                if not item or len(item) < 2:
                    continue
                    
                command = item[0]
                
                if command == 'putText' and len(item) >= 6:
                    # 绘制文本：['putText', text, position, font, scale, color, thickness]
                    text = str(item[1])
                    position = tuple(item[2]) if isinstance(item[2], (list, tuple)) else item[2]
                    font = item[3] if len(item) > 3 else cv2.FONT_HERSHEY_SIMPLEX
                    scale = item[4] if len(item) > 4 else 1.0
                    color = tuple(item[5]) if len(item) > 5 else (0, 255, 0)
                    thickness = item[6] if len(item) > 6 else 2
                    
                    cv2.putText(result_image, text, position, font, scale, color, thickness, cv2.LINE_AA)
                    
                elif command == 'circle' and len(item) >= 5:
                    # 绘制圆形：['circle', center, radius, color, thickness]
                    center = tuple(item[1]) if isinstance(item[1], (list, tuple)) else item[1]
                    radius = int(item[2])
                    color = tuple(item[3]) if len(item) > 3 else (0, 255, 0)
                    thickness = item[4] if len(item) > 4 else 2
                    
                    cv2.circle(result_image, center, radius, color, thickness)
                    
                elif command == 'rectangle' and len(item) >= 5:
                    # 绘制矩形：['rectangle', pt1, pt2, color, thickness]
                    pt1 = tuple(item[1]) if isinstance(item[1], (list, tuple)) else item[1]
                    pt2 = tuple(item[2]) if isinstance(item[2], (list, tuple)) else item[2]
                    color = tuple(item[3]) if len(item) > 3 else (0, 255, 0)
                    thickness = item[4] if len(item) > 4 else 2
                    
                    cv2.rectangle(result_image, pt1, pt2, color, thickness)
                    
            return result_image
            
        except Exception as e:
            logger.error(f"绘制检测结果失败：{e}")
            return image
            
    @staticmethod
    def convert_color_space(image: np.ndarray, conversion_code: int) -> np.ndarray:
        """
        转换颜色空间
        Args:
            image: 输入图像
            conversion_code: OpenCV颜色转换代码
        Returns:
            np.ndarray: 转换后的图像
        """
        try:
            if image is None:
                return None
                
            return cv2.cvtColor(image, conversion_code)
            
        except Exception as e:
            logger.error(f"颜色空间转换失败：{e}")
            return image
            
    @staticmethod
    def validate_image(image: np.ndarray) -> bool:
        """
        验证图像是否有效
        Args:
            image: 输入图像
        Returns:
            bool: 图像是否有效
        """
        if image is None:
            return False
            
        if not isinstance(image, np.ndarray):
            return False
            
        if image.size == 0:
            return False
            
        if len(image.shape) not in [2, 3]:
            return False
            
        if len(image.shape) == 3 and image.shape[2] not in [1, 3, 4]:
            return False
            
        return True
