"""
测试相机控制器修复
"""
import sys
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from core.camera_controller import CameraController

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_camera_controller():
    """测试相机控制器"""
    app = QApplication(sys.argv)
    
    # 创建相机控制器
    camera = CameraController()
    
    def on_image_captured(image):
        print(f"图像采集成功，尺寸：{image.shape}")
        
    def on_camera_error(error):
        print(f"相机错误：{error}")
        
    def on_status_changed(status):
        print(f"相机状态变化：{status}")
    
    # 连接信号
    camera.image_captured.connect(on_image_captured)
    camera.camera_error.connect(on_camera_error)
    camera.camera_status_changed.connect(on_status_changed)
    
    # 尝试初始化相机
    print("正在初始化相机...")
    success = camera.initialize()
    
    if success:
        print("相机初始化成功！")
        
        # 设置定时器测试手动触发
        def test_trigger():
            print("测试手动触发...")
            camera.trigger_capture()
            
        timer = QTimer()
        timer.timeout.connect(test_trigger)
        timer.start(2000)  # 每2秒触发一次
        
        # 运行5秒后退出
        QTimer.singleShot(5000, app.quit)
        
    else:
        print("相机初始化失败！")
        QTimer.singleShot(1000, app.quit)
    
    # 运行应用程序
    try:
        app.exec()
    finally:
        camera.close()
        print("测试完成")

if __name__ == "__main__":
    test_camera_controller()
