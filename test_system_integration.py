"""
系统集成测试 - 不依赖硬件的模拟测试
"""
import sys
import logging
import numpy as np
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, QObject, pyqtSignal

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 模拟 gxipy 模块
class MockGxipy:
    class DeviceManager:
        def update_all_device_list(self):
            return 1, [{"sn": "MOCK_CAMERA_001"}]
            
        def open_device_by_sn(self, sn):
            return MockCamera()
            
        def create_image_format_convert(self):
            return MockImageFormatConvert()
            
        def create_image_process(self):
            return MockImageProcess()
    
    class GxPixelFormatEntry:
        GX_PIXEL_FORMAT_BAYER_RG8 = "BAYER_RG8"

class MockCamera:
    def __init__(self):
        self.data_stream = [MockDataStream()]
        
    def get_remote_device_feature_control(self):
        return MockRemoteDeviceFeature()
        
    def create_image_process_config(self):
        return MockImageProcessConfig()
        
    def stream_on(self):
        print("模拟相机开始采集")
        
    def stream_off(self):
        print("模拟相机停止采集")
        
    def close_device(self):
        print("模拟相机设备关闭")

class MockDataStream:
    def __init__(self):
        self.callback = None
        
    def register_capture_callback(self, callback):
        if hasattr(callback, '__self__'):
            raise TypeError("Expected callback type is function not <class 'method'>")
        self.callback = callback
        print("模拟回调函数注册成功")
        
    def unregister_capture_callback(self):
        self.callback = None
        print("模拟回调函数取消注册")

class MockRemoteDeviceFeature:
    def get_enum_feature(self, name):
        return MockEnumFeature()
        
    def get_float_feature(self, name):
        return MockFloatFeature()

class MockEnumFeature:
    def set(self, value):
        print(f"设置枚举特性：{value}")
        
class MockFloatFeature:
    def set(self, value):
        print(f"设置浮点特性：{value}")
        
    def get(self):
        return 1.0

class MockImageProcessConfig:
    pass

class MockImageFormatConvert:
    pass

class MockImageProcess:
    pass

# 替换 gxipy 模块
sys.modules['gxipy'] = MockGxipy()

# 现在可以导入我们的模块
from core.camera_controller import CameraController
from core.app_manager import ApplicationManager
from core.exception_handler import setup_global_exception_handling

def test_camera_controller():
    """测试相机控制器"""
    print("\n=== 测试相机控制器 ===")
    
    camera = CameraController()
    
    def on_image_captured(image):
        print(f"图像采集成功，尺寸：{image.shape}")
        
    def on_camera_error(error):
        print(f"相机错误：{error}")
        
    def on_status_changed(status):
        print(f"相机状态变化：{status}")
    
    # 连接信号
    camera.image_captured.connect(on_image_captured)
    camera.camera_error.connect(on_camera_error)
    camera.camera_status_changed.connect(on_status_changed)
    
    # 测试初始化
    success = camera.initialize()
    print(f"相机初始化：{'成功' if success else '失败'}")
    
    if success:
        # 测试参数设置
        camera.set_exposure_time(1000)
        camera.set_gain(1.5)
        
        # 模拟图像采集
        mock_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        if camera._capture_callback:
            # 创建模拟的原始图像数据
            class MockRawImage:
                def __init__(self):
                    self.frame_data = MockFrameData()
                    
            class MockFrameData:
                def __init__(self):
                    self.pixel_format = MockGxipy.GxPixelFormatEntry.GX_PIXEL_FORMAT_BAYER_RG8
                    self.width = 640
                    self.height = 480
                    
            mock_raw = MockRawImage()
            camera._capture_callback(mock_raw)
    
    # 清理
    camera.close()
    print("相机控制器测试完成")

def test_app_manager():
    """测试应用程序管理器"""
    print("\n=== 测试应用程序管理器 ===")
    
    app_manager = ApplicationManager()
    
    def on_system_status(component, status):
        print(f"系统状态：{component} - {status}")
        
    def on_error(component, error):
        print(f"系统错误：{component} - {error}")
    
    # 连接信号
    app_manager.system_status_changed.connect(on_system_status)
    app_manager.error_occurred.connect(on_error)
    
    # 测试初始化
    success = app_manager.initialize()
    print(f"应用程序管理器初始化：{'成功' if success else '失败'}")
    
    if success:
        # 测试启动系统
        app_manager.start_system()
        
        # 测试手动触发
        result = app_manager.manual_trigger()
        print(f"手动触发：{'成功' if result else '失败'}")
    
    # 清理
    app_manager.stop_system()
    print("应用程序管理器测试完成")

def main():
    """主测试函数"""
    print("=== 系统集成测试开始 ===")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 设置全局异常处理
    global_handler = setup_global_exception_handling(app)
    
    try:
        # 测试各个组件
        test_camera_controller()
        test_app_manager()
        
        print("\n=== 所有测试完成 ===")
        print("✅ 相机回调函数修复成功")
        print("✅ 模块化架构工作正常")
        print("✅ 异常处理机制正常")
        
    except Exception as e:
        print(f"测试过程中出现错误：{e}")
        
    finally:
        # 退出应用程序
        QTimer.singleShot(100, app.quit)
        app.exec()

if __name__ == "__main__":
    main()
