"""
测试回调函数修复的模拟测试
"""
import weakref

class MockDataStream:
    """模拟数据流类"""
    def __init__(self):
        self.callback = None
        
    def register_capture_callback(self, callback):
        """注册回调函数"""
        # 检查回调是否是函数而不是方法
        if hasattr(callback, '__self__'):
            raise TypeError("Expected callback type is function not <class 'method'>")
        
        self.callback = callback
        print("回调函数注册成功！")
        
    def unregister_capture_callback(self):
        """取消注册回调函数"""
        self.callback = None
        print("回调函数已取消注册")
        
    def simulate_capture(self, image_data):
        """模拟图像采集"""
        if self.callback:
            self.callback(image_data)

class MockCamera:
    """模拟相机类"""
    def __init__(self):
        self.data_stream = [MockDataStream()]

class TestCameraController:
    """测试相机控制器"""
    def __init__(self):
        self.cam = MockCamera()
        self._capture_callback = None
        
    def initialize_old_way(self):
        """旧的初始化方式（会失败）"""
        try:
            # 这种方式会失败，因为传递的是方法而不是函数
            self._capture_callback = self._on_image_captured
            self.cam.data_stream[0].register_capture_callback(self._capture_callback)
            return True
        except Exception as e:
            print(f"旧方式失败：{e}")
            return False
            
    def initialize_new_way(self):
        """新的初始化方式（应该成功）"""
        try:
            # 使用弱引用避免循环引用
            weak_self = weakref.ref(self)
            
            def capture_callback(raw_image):
                """独立的图像采集回调函数"""
                try:
                    self_ref = weak_self()
                    if self_ref is not None:
                        self_ref._on_image_captured(raw_image)
                except Exception as e:
                    print(f"图像采集回调处理失败：{e}")
                    
            self._capture_callback = capture_callback
            self.cam.data_stream[0].register_capture_callback(self._capture_callback)
            return True
        except Exception as e:
            print(f"新方式失败：{e}")
            return False
            
    def _on_image_captured(self, raw_image):
        """图像采集回调处理"""
        print(f"成功处理图像：{raw_image}")
        
    def cleanup(self):
        """清理资源"""
        if self.cam and self._capture_callback:
            self.cam.data_stream[0].unregister_capture_callback()
            self._capture_callback = None

def test_callback_fix():
    """测试回调函数修复"""
    print("=== 测试回调函数修复 ===")
    
    # 测试旧方式
    print("\n1. 测试旧方式（应该失败）：")
    controller1 = TestCameraController()
    success1 = controller1.initialize_old_way()
    print(f"旧方式结果：{'成功' if success1 else '失败'}")
    
    # 测试新方式
    print("\n2. 测试新方式（应该成功）：")
    controller2 = TestCameraController()
    success2 = controller2.initialize_new_way()
    print(f"新方式结果：{'成功' if success2 else '失败'}")
    
    if success2:
        # 模拟图像采集
        print("\n3. 模拟图像采集：")
        controller2.cam.data_stream[0].simulate_capture("模拟图像数据")
        
        # 清理资源
        print("\n4. 清理资源：")
        controller2.cleanup()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_callback_fix()
