"""
简单的绘制修复测试
"""
import numpy as np

def ensure_tuple(value, expected_length: int):
    """确保值是指定长度的元组"""
    try:
        if isinstance(value, (list, tuple)):
            if len(value) >= expected_length:
                return tuple(value[:expected_length])
            else:
                # 如果长度不够，用0填充
                return tuple(list(value) + [0] * (expected_length - len(value)))
        elif isinstance(value, (int, float)):
            # 如果是单个数值，复制到所有位置
            return tuple([int(value)] * expected_length)
        else:
            return None
    except Exception:
        return None

def test_ensure_tuple_function():
    """测试ensure_tuple函数"""
    print("=== 测试ensure_tuple函数 ===")
    
    # 测试各种输入
    test_cases = [
        # (输入值, 期望长度, 期望结果, 描述)
        ([10, 20], 2, (10, 20), "正常的列表"),
        ((30, 40), 2, (30, 40), "正常的元组"),
        ([10], 2, (10, 0), "长度不够的列表"),
        (50, 2, (50, 50), "单个整数"),
        (60.5, 3, (60, 60, 60), "单个浮点数"),
        ([10, 20, 30], 2, (10, 20), "长度超出的列表"),
        (None, 2, None, "None值"),
        ("invalid", 2, None, "无效字符串"),
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for input_val, length, expected, desc in test_cases:
        try:
            result = ensure_tuple(input_val, length)
            if result == expected:
                print(f"✅ {desc}: {input_val} -> {result}")
                success_count += 1
            else:
                print(f"❌ {desc}: {input_val} -> {result}, 期望: {expected}")
        except Exception as e:
            print(f"❌ {desc}: 异常 {e}")
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 通过")
    return success_count == total_count

def test_draw_data_processing():
    """测试绘制数据处理逻辑"""
    print("\n=== 测试绘制数据处理逻辑 ===")
    
    # 模拟原来会出错的数据
    problematic_data = [
        ['putText', 'Hello', 100, 0, 1.0, 255, 2],  # position和color是int
        ['circle', 200, 20, 128, 2],  # center和color是int
        ['rectangle', 300, 400, 64, 2]  # pt1, pt2, color都是int
    ]
    
    success_count = 0
    total_count = len(problematic_data)
    
    for item in problematic_data:
        try:
            command = item[0]
            print(f"\n🔧 处理命令: {command}")
            
            if command == 'putText' and len(item) >= 6:
                position = ensure_tuple(item[2], 2)
                color = ensure_tuple(item[5], 3)
                print(f"  position: {item[2]} -> {position}")
                print(f"  color: {item[5]} -> {color}")
                
                if position and color:
                    print(f"✅ putText 数据处理成功")
                    success_count += 1
                else:
                    print(f"❌ putText 数据处理失败")
                    
            elif command == 'circle' and len(item) >= 5:
                center = ensure_tuple(item[1], 2)
                color = ensure_tuple(item[3], 3)
                print(f"  center: {item[1]} -> {center}")
                print(f"  color: {item[3]} -> {color}")
                
                if center and color:
                    print(f"✅ circle 数据处理成功")
                    success_count += 1
                else:
                    print(f"❌ circle 数据处理失败")
                    
            elif command == 'rectangle' and len(item) >= 5:
                pt1 = ensure_tuple(item[1], 2)
                pt2 = ensure_tuple(item[2], 2)
                color = ensure_tuple(item[3], 3)
                print(f"  pt1: {item[1]} -> {pt1}")
                print(f"  pt2: {item[2]} -> {pt2}")
                print(f"  color: {item[3]} -> {color}")
                
                if pt1 and pt2 and color:
                    print(f"✅ rectangle 数据处理成功")
                    success_count += 1
                else:
                    print(f"❌ rectangle 数据处理失败")
                    
        except Exception as e:
            print(f"❌ 处理 {command} 时出错: {e}")
    
    print(f"\n📊 绘制数据处理结果: {success_count}/{total_count} 成功")
    return success_count == total_count

def test_original_error_scenario():
    """测试原始错误场景"""
    print("\n=== 测试原始错误场景 ===")
    
    # 模拟会导致 'int' object is not iterable 错误的情况
    print("🔧 模拟原始错误...")
    
    try:
        # 这是原来会出错的代码逻辑
        problematic_value = 255  # 单个整数
        
        # 原来的代码会这样做：
        # color = tuple(problematic_value)  # 这会出错
        
        print(f"原始值: {problematic_value} (类型: {type(problematic_value)})")
        
        # 现在的修复代码：
        fixed_color = ensure_tuple(problematic_value, 3)
        print(f"修复后: {fixed_color}")
        
        if fixed_color:
            print("✅ 原始错误场景已修复")
            return True
        else:
            print("❌ 修复失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试原始错误场景时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 绘制检测结果修复测试 ===\n")
    
    test1 = test_ensure_tuple_function()
    test2 = test_draw_data_processing()
    test3 = test_original_error_scenario()
    
    print(f"\n=== 测试总结 ===")
    
    if test1 and test2 and test3:
        print("🎉 所有测试通过！")
        print("✅ ensure_tuple函数工作正常")
        print("✅ 绘制数据处理逻辑正确")
        print("✅ 原始错误场景已修复")
        print("\n📋 修复内容:")
        print("1. 添加了_ensure_tuple辅助函数")
        print("2. 处理各种数据类型的坐标和颜色值")
        print("3. 单个数值会被转换为合适的元组")
        print("4. 无效数据会被安全处理")
        print("5. 添加了空值检查避免绘制失败")
        print("\n🚀 现在不会再出现 'int' object is not iterable 错误了！")
    else:
        print("❌ 部分测试失败")
        if not test1:
            print("  - ensure_tuple函数需要进一步修复")
        if not test2:
            print("  - 绘制数据处理逻辑需要进一步修复")
        if not test3:
            print("  - 原始错误场景修复不完整")

if __name__ == "__main__":
    main()
