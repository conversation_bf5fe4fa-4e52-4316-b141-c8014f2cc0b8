"""
Modbus通信控制器模块
负责与PLC的Modbus通信
"""
import asyncio
import logging
from typing import Optional, List
from PyQt5.QtCore import QObject, QThread, pyqtSignal, QMutex, QMutexLocker
from pymodbus.client import AsyncModbusTcpClient

logger = logging.getLogger(__name__)


class ModbusController(QObject):
    """Modbus通信控制器"""
    
    # 信号定义
    trigger_received = pyqtSignal()  # 收到触发信号
    connection_status_changed = pyqtSignal(bool)  # 连接状态变化
    modbus_error = pyqtSignal(str)  # Modbus错误信号
    
    def __init__(self, host: str = '************', port: int = 502):
        super().__init__()
        self.host = host
        self.port = port
        self.client: Optional[AsyncModbusTcpClient] = None
        self.is_connected = False
        self.is_checking = False
        self.reply_queue: List[int] = []
        self._mutex = QMutex()
        
    async def connect(self) -> bool:
        """
        连接到Modbus服务器
        Returns:
            bool: 连接是否成功
        """
        try:
            logger.info(f"正在连接Modbus服务器 {self.host}:{self.port}")
            self.client = AsyncModbusTcpClient(host=self.host, port=self.port)
            result = await self.client.connect()
            
            if result:
                self.is_connected = True
                self.connection_status_changed.emit(True)
                logger.info("Modbus连接成功")
                return True
            else:
                error_msg = "Modbus连接失败"
                logger.error(error_msg)
                self.modbus_error.emit(error_msg)
                return False
                
        except Exception as e:
            error_msg = f"Modbus连接异常：{e}"
            logger.error(error_msg)
            self.modbus_error.emit(error_msg)
            return False
            
    async def disconnect(self):
        """断开Modbus连接"""
        try:
            if self.client and self.is_connected:
                await self.client.close()
                self.is_connected = False
                self.connection_status_changed.emit(False)
                logger.info("Modbus连接已断开")
        except Exception as e:
            logger.error(f"断开Modbus连接时出错：{e}")
            
    async def read_register(self, address: int, count: int = 1) -> Optional[List[int]]:
        """
        读取保持寄存器
        Args:
            address: 寄存器地址
            count: 读取数量
        Returns:
            Optional[List[int]]: 读取的值列表，失败返回None
        """
        try:
            if not self.client or not self.is_connected:
                return None
                
            response = await self.client.read_holding_registers(address=address, count=count)
            if not response.isError():
                return response.registers
            else:
                logger.warning(f"读取寄存器{address}失败：{response}")
                return None
                
        except Exception as e:
            logger.error(f"读取寄存器{address}异常：{e}")
            return None
            
    async def write_register(self, address: int, value: int) -> bool:
        """
        写入单个寄存器
        Args:
            address: 寄存器地址
            value: 写入值
        Returns:
            bool: 写入是否成功
        """
        try:
            if not self.client or not self.is_connected:
                return False
                
            response = await self.client.write_register(address, value)
            if not response.isError():
                logger.debug(f"写入寄存器{address}={value}成功")
                return True
            else:
                logger.warning(f"写入寄存器{address}={value}失败：{response}")
                return False
                
        except Exception as e:
            logger.error(f"写入寄存器{address}={value}异常：{e}")
            return False
            
    def add_reply_value(self, value: int):
        """
        添加回复值到队列
        Args:
            value: 回复值
        """
        with QMutexLocker(self._mutex):
            self.reply_queue.append(value)
            logger.debug(f"添加回复值：{value}，队列长度：{len(self.reply_queue)}")
            
    def get_reply_value(self) -> Optional[int]:
        """
        从队列获取回复值
        Returns:
            Optional[int]: 回复值，队列为空返回None
        """
        with QMutexLocker(self._mutex):
            if self.reply_queue:
                value = self.reply_queue.pop(0)
                logger.debug(f"获取回复值：{value}，剩余队列长度：{len(self.reply_queue)}")
                return value
            return None
            
    def clear_reply_queue(self):
        """清空回复队列"""
        with QMutexLocker(self._mutex):
            self.reply_queue.clear()
            logger.info("回复队列已清空")


class ModbusWorkerThread(QThread):
    """Modbus工作线程"""
    
    def __init__(self, controller: ModbusController):
        super().__init__()
        self.controller = controller
        self._running = True
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        
    def run(self):
        """线程主循环"""
        try:
            # 创建新的事件循环
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            
            # 运行主要的异步任务
            self.loop.run_until_complete(self._main_loop())
            
        except Exception as e:
            logger.error(f"Modbus工作线程异常：{e}")
        finally:
            if self.loop:
                self.loop.close()
                
    async def _main_loop(self):
        """主要的异步循环"""
        try:
            # 连接到Modbus服务器
            if not await self.controller.connect():
                return
                
            # 主循环
            while self._running:
                try:
                    # 检查触发信号（地址2000）
                    await self._check_trigger_signal()
                    
                    # 处理回复信号（地址2001）
                    await self._handle_reply_signal()
                    
                    # 短暂延时
                    await asyncio.sleep(0.05)
                    
                except Exception as e:
                    logger.error(f"Modbus主循环异常：{e}")
                    await asyncio.sleep(1)  # 出错时延长等待时间
                    
        finally:
            await self.controller.disconnect()
            
    async def _check_trigger_signal(self):
        """检查触发信号"""
        try:
            values = await self.controller.read_register(2000, 1)
            if values and values[0] == 1 and not self.controller.is_checking:
                self.controller.is_checking = True
                await asyncio.sleep(0.2)  # 防抖延时
                self.controller.trigger_received.emit()
                logger.debug("收到触发信号")
                
        except Exception as e:
            logger.error(f"检查触发信号异常：{e}")
            
    async def _handle_reply_signal(self):
        """处理回复信号"""
        try:
            values = await self.controller.read_register(2001, 1)
            if values and values[0] == 1:
                reply_value = self.controller.get_reply_value()
                if reply_value is not None:
                    success = await self.controller.write_register(2001, reply_value)
                    if success:
                        logger.debug(f"回复信号已发送：{reply_value}")
                        
        except Exception as e:
            logger.error(f"处理回复信号异常：{e}")
            
    def stop(self):
        """停止线程"""
        logger.info("正在停止Modbus工作线程...")
        self._running = False
        
        # 如果事件循环正在运行，安全地停止它
        if self.loop and self.loop.is_running():
            self.loop.call_soon_threadsafe(self.loop.stop)
            
    def write_register_async(self, address: int, value: int):
        """
        异步写入寄存器（从其他线程调用）
        Args:
            address: 寄存器地址
            value: 写入值
        """
        if self.loop and self.loop.is_running():
            asyncio.run_coroutine_threadsafe(
                self.controller.write_register(address, value), 
                self.loop
            )


class ModbusManager(QObject):
    """Modbus管理器，提供高级接口"""
    
    # 信号定义
    trigger_received = pyqtSignal()
    connection_status_changed = pyqtSignal(bool)
    modbus_error = pyqtSignal(str)
    
    def __init__(self, host: str = '************', port: int = 502):
        super().__init__()
        self.controller = ModbusController(host, port)
        self.worker_thread: Optional[ModbusWorkerThread] = None
        
        # 连接信号
        self.controller.trigger_received.connect(self._on_trigger_received)
        self.controller.connection_status_changed.connect(self.connection_status_changed)
        self.controller.modbus_error.connect(self.modbus_error)
        
    def start(self):
        """启动Modbus通信"""
        if not self.worker_thread or not self.worker_thread.isRunning():
            logger.info("启动Modbus通信...")
            self.worker_thread = ModbusWorkerThread(self.controller)
            self.worker_thread.start()
            
    def stop(self):
        """停止Modbus通信"""
        if self.worker_thread and self.worker_thread.isRunning():
            logger.info("停止Modbus通信...")
            self.worker_thread.stop()
            self.worker_thread.wait(3000)  # 等待最多3秒
            
    def _on_trigger_received(self):
        """处理收到的触发信号"""
        self.trigger_received.emit()
        # 立即回复确认信号
        self.write_register(2000, 3)
        
    def write_register(self, address: int, value: int):
        """
        写入寄存器
        Args:
            address: 寄存器地址
            value: 写入值
        """
        if self.worker_thread:
            self.worker_thread.write_register_async(address, value)
            
    def add_reply_value(self, value: int):
        """
        添加回复值
        Args:
            value: 回复值
        """
        self.controller.add_reply_value(value)
        
    def reset_checking_state(self):
        """重置检测状态"""
        self.controller.is_checking = False
        
    def clear_reply_queue(self):
        """清空回复队列"""
        self.controller.clear_reply_queue()
