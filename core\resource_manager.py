"""
资源管理器
负责系统资源的统一管理和清理
"""
import os
import gc
import logging
import threading
import weakref
from typing import Dict, List, Any, Optional, Callable
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class ResourceInfo:
    """资源信息"""
    name: str
    resource_type: str
    created_at: datetime
    cleanup_func: Optional[Callable] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class ResourceManager:
    """资源管理器，确保正确释放资源"""
    
    def __init__(self):
        self._resources: Dict[str, ResourceInfo] = {}
        self._lock = threading.RLock()
        self._cleanup_callbacks: List[Callable] = []
        self._is_shutting_down = False
        
    def register_resource(self, 
                         name: str, 
                         resource: Any, 
                         resource_type: str,
                         cleanup_func: Optional[Callable] = None,
                         metadata: Optional[Dict[str, Any]] = None) -> str:
        """注册资源"""
        with self._lock:
            if self._is_shutting_down:
                logger.warning(f"系统正在关闭，无法注册资源：{name}")
                return name
                
            resource_info = ResourceInfo(
                name=name,
                resource_type=resource_type,
                created_at=datetime.now(),
                cleanup_func=cleanup_func,
                metadata=metadata or {}
            )
            
            # 使用弱引用避免循环引用
            if hasattr(resource, '__del__'):
                resource_info.metadata['weak_ref'] = weakref.ref(resource)
                
            self._resources[name] = resource_info
            logger.debug(f"已注册资源：{name} ({resource_type})")
            
            return name
            
    def unregister_resource(self, name: str) -> bool:
        """注销资源"""
        with self._lock:
            if name in self._resources:
                resource_info = self._resources.pop(name)
                
                # 执行清理函数
                if resource_info.cleanup_func:
                    try:
                        resource_info.cleanup_func()
                        logger.debug(f"已清理资源：{name}")
                    except Exception as e:
                        logger.error(f"清理资源失败 {name}：{e}")
                        
                return True
            return False
            
    def cleanup_resource_type(self, resource_type: str):
        """清理指定类型的所有资源"""
        with self._lock:
            resources_to_cleanup = [
                name for name, info in self._resources.items() 
                if info.resource_type == resource_type
            ]
            
            for name in resources_to_cleanup:
                self.unregister_resource(name)
                
            logger.info(f"已清理 {len(resources_to_cleanup)} 个 {resource_type} 类型资源")
            
    def get_resource_info(self, name: str) -> Optional[ResourceInfo]:
        """获取资源信息"""
        with self._lock:
            return self._resources.get(name)
            
    def list_resources(self, resource_type: Optional[str] = None) -> List[ResourceInfo]:
        """列出资源"""
        with self._lock:
            if resource_type:
                return [
                    info for info in self._resources.values() 
                    if info.resource_type == resource_type
                ]
            return list(self._resources.values())
            
    def add_cleanup_callback(self, callback: Callable):
        """添加清理回调"""
        with self._lock:
            self._cleanup_callbacks.append(callback)
            
    def remove_cleanup_callback(self, callback: Callable):
        """移除清理回调"""
        with self._lock:
            if callback in self._cleanup_callbacks:
                self._cleanup_callbacks.remove(callback)
                
    def cleanup_all(self):
        """清理所有资源"""
        with self._lock:
            self._is_shutting_down = True
            
            logger.info("开始清理所有资源...")
            
            # 执行清理回调
            for callback in self._cleanup_callbacks:
                try:
                    callback()
                except Exception as e:
                    logger.error(f"执行清理回调失败：{e}")
                    
            # 清理注册的资源
            resource_names = list(self._resources.keys())
            for name in resource_names:
                self.unregister_resource(name)
                
            # 强制垃圾回收
            gc.collect()
            
            logger.info(f"已清理 {len(resource_names)} 个资源")
            
    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss': memory_info.rss,  # 物理内存
            'vms': memory_info.vms,  # 虚拟内存
            'percent': process.memory_percent(),
            'resource_count': len(self._resources),
            'resource_types': {
                resource_type: len([
                    r for r in self._resources.values() 
                    if r.resource_type == resource_type
                ])
                for resource_type in set(r.resource_type for r in self._resources.values())
            }
        }
        
    @contextmanager
    def managed_resource(self, 
                        name: str, 
                        resource: Any, 
                        resource_type: str,
                        cleanup_func: Optional[Callable] = None):
        """上下文管理器，自动管理资源生命周期"""
        try:
            self.register_resource(name, resource, resource_type, cleanup_func)
            yield resource
        finally:
            self.unregister_resource(name)
            
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            self.cleanup_all()
        except Exception as e:
            logger.error(f"ResourceManager析构时出错：{e}")


class FileManager:
    """文件管理器，管理临时文件和目录"""
    
    def __init__(self, resource_manager: ResourceManager):
        self.resource_manager = resource_manager
        self._temp_files: List[str] = []
        self._temp_dirs: List[str] = []
        
    def create_temp_file(self, suffix: str = "", prefix: str = "temp_") -> str:
        """创建临时文件"""
        import tempfile
        
        fd, path = tempfile.mkstemp(suffix=suffix, prefix=prefix)
        os.close(fd)  # 关闭文件描述符
        
        self._temp_files.append(path)
        self.resource_manager.register_resource(
            f"temp_file_{path}",
            path,
            "temp_file",
            lambda: self._cleanup_file(path)
        )
        
        logger.debug(f"创建临时文件：{path}")
        return path
        
    def create_temp_dir(self, prefix: str = "temp_dir_") -> str:
        """创建临时目录"""
        import tempfile
        import shutil
        
        path = tempfile.mkdtemp(prefix=prefix)
        self._temp_dirs.append(path)
        
        self.resource_manager.register_resource(
            f"temp_dir_{path}",
            path,
            "temp_dir",
            lambda: shutil.rmtree(path, ignore_errors=True)
        )
        
        logger.debug(f"创建临时目录：{path}")
        return path
        
    def _cleanup_file(self, path: str):
        """清理文件"""
        try:
            if os.path.exists(path):
                os.remove(path)
                logger.debug(f"已删除临时文件：{path}")
        except Exception as e:
            logger.error(f"删除临时文件失败 {path}：{e}")
            
    def cleanup_temp_files(self):
        """清理所有临时文件"""
        for path in self._temp_files[:]:
            self._cleanup_file(path)
            if path in self._temp_files:
                self._temp_files.remove(path)
                
        logger.info(f"已清理 {len(self._temp_files)} 个临时文件")
        
    def cleanup_temp_dirs(self):
        """清理所有临时目录"""
        import shutil
        
        for path in self._temp_dirs[:]:
            try:
                if os.path.exists(path):
                    shutil.rmtree(path, ignore_errors=True)
                    logger.debug(f"已删除临时目录：{path}")
            except Exception as e:
                logger.error(f"删除临时目录失败 {path}：{e}")
            finally:
                if path in self._temp_dirs:
                    self._temp_dirs.remove(path)
                    
        logger.info(f"已清理 {len(self._temp_dirs)} 个临时目录")


class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, resource_manager: ResourceManager, threshold_mb: float = 500.0):
        self.resource_manager = resource_manager
        self.threshold_mb = threshold_mb
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        
    def start_monitoring(self, interval: float = 30.0):
        """开始内存监控"""
        if self._monitoring:
            return
            
        self._monitoring = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self._monitor_thread.start()
        logger.info(f"内存监控已启动，阈值：{self.threshold_mb}MB")
        
    def stop_monitoring(self):
        """停止内存监控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
        logger.info("内存监控已停止")
        
    def _monitor_loop(self, interval: float):
        """监控循环"""
        import time
        
        while self._monitoring:
            try:
                usage = self.resource_manager.get_memory_usage()
                memory_mb = usage['rss'] / 1024 / 1024
                
                if memory_mb > self.threshold_mb:
                    logger.warning(f"内存使用过高：{memory_mb:.1f}MB (阈值：{self.threshold_mb}MB)")
                    
                    # 触发垃圾回收
                    gc.collect()
                    
                    # 记录详细信息
                    logger.info(f"资源统计：{usage['resource_types']}")
                    
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"内存监控出错：{e}")
                time.sleep(interval)


# 全局资源管理器实例
_global_resource_manager: Optional[ResourceManager] = None


def get_resource_manager() -> ResourceManager:
    """获取全局资源管理器"""
    global _global_resource_manager
    if _global_resource_manager is None:
        _global_resource_manager = ResourceManager()
    return _global_resource_manager


def cleanup_global_resources():
    """清理全局资源"""
    global _global_resource_manager
    if _global_resource_manager:
        _global_resource_manager.cleanup_all()
        _global_resource_manager = None
