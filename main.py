import os
import sys
import logging
from typing import Optional

import cv2
import numpy as np
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtWidgets import QFrame, QHBoxLayout, QApplication, QVBoxLayout
from qfluentwidgets import (
    FluentWindow, SubtitleLabel, ComboBox, GroupHeaderCardWidget,
    BodyLabel, PrimaryPushButton, DoubleSpinBox, InfoBar, InfoBarPosition,
    MessageBox, PushButton, TextBrowser
)
from qfluentwidgets import FluentIcon as FIF



# 配置日志系统
def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('detection_app.log', mode='a', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

# 初始化日志
setup_logging()
logger = logging.getLogger(__name__)

# 导入新的UI组件
from ui.components import FullscreenImageViewer, LoginDialog, ClickableImageLabel, ThreadSafeLogHandler, ImageProcessor
from ui.responsive_ui import UIResponsiveManager, AsyncTaskManager
from core.app_manager import ApplicationManager
from core.exception_handler import setup_global_exception_handling, exception_handler
from core import DetectionResult

# 移除旧的线程类，现在使用新的模块化组件

class MainWindow(FluentWindow):
    """主窗口类，使用新的模块化架构"""

    def __init__(self):
        super().__init__()

        # 初始化应用程序管理器
        self.app_manager = ApplicationManager()

        # UI响应性管理
        self.ui_manager = UIResponsiveManager(self)
        self.task_manager = AsyncTaskManager(self.ui_manager)

        # UI相关属性
        self.source_pixmap: Optional[QPixmap] = None
        self.result_pixmap: Optional[QPixmap] = None
        self.log_handler: Optional[ThreadSafeLogHandler] = None

        # 初始化窗口
        self._init_window()
        self._init_interface()
        self._setup_connections()
        self._setup_logging()

        # 启动UI响应性管理
        self.ui_manager.start_keep_alive()

        # 启动系统
        self._start_system()

    def _init_window(self):
        """初始化窗口"""
        self.resize(1024, 600)
        icon_path = os.path.join(os.path.dirname(__file__), "icon.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        self.setWindowTitle('兆威8150视觉检测系统')
        self.showMaximized()

    def _setup_logging(self):
        """设置日志处理器"""
        try:
            self.log_handler = ThreadSafeLogHandler(self.log_text)
            self.log_handler.setLevel(logging.INFO)

            # 添加到根日志记录器
            root_logger = logging.getLogger()
            root_logger.addHandler(self.log_handler)

            logger.info("视觉检测系统界面已启动")

        except Exception as e:
            logger.error(f"设置日志处理器失败：{e}")

    def _setup_connections(self):
        """设置信号连接"""
        try:
            # 连接应用程序管理器信号
            self.app_manager.image_captured.connect(self._on_image_captured)
            self.app_manager.detection_completed.connect(self._on_detection_completed)
            self.app_manager.system_status_changed.connect(self._on_system_status_changed)
            self.app_manager.error_occurred.connect(self._on_error_occurred)

            # 连接UI控件信号
            self.save_btn.clicked.connect(self._save_settings)
            self.take_img_btn.clicked.connect(self._manual_trigger)
            self.init_process_btn.clicked.connect(self._init_process)
            self.model_combo.currentIndexChanged.connect(self._on_model_changed)

            # 连接图像点击信号
            self.source_image.clicked.connect(lambda: self._show_fullscreen_image(self.source_pixmap))
            self.result_image.clicked.connect(lambda: self._show_fullscreen_image(self.result_pixmap))

            logger.debug("信号连接设置完成")

        except Exception as e:
            logger.error(f"设置信号连接失败：{e}")

    @exception_handler(component="MainWindow", user_message="系统启动失败")
    def _start_system(self):
        """启动系统"""
        # 延迟启动，确保UI完全初始化
        QTimer.singleShot(1000, self.app_manager.start_system)

    @exception_handler(component="MainWindow", user_message="手动触发检测失败")
    def _manual_trigger(self):
        """手动触发检测"""
        # 使用异步任务管理器执行手动触发
        self.task_manager.run_async_task(
            task_id="manual_trigger",
            description="手动触发检测",
            work_func=self.app_manager.manual_trigger,
            show_progress=True,
            parent=self
        )

    def _init_process(self):
        """初始化处理"""
        try:
            self.app_manager.clear_detection_queue()
            logger.info("系统已重新初始化")

        except Exception as e:
            logger.error(f"初始化处理失败：{e}")

    def _on_model_changed(self, index: int):
        """模型选择改变"""
        try:
            self.app_manager.set_model_index(index)

        except Exception as e:
            logger.error(f"切换模型失败：{e}")


    def _init_interface(self):
        """初始化界面"""
        try:
            # 创建主界面
            self._create_home_interface()

            # 创建设置界面
            self._create_settings_interface()

            # 添加到导航
            self.addSubInterface(self.home_interface, FIF.HOME, '检测')
            self.addSubInterface(self.settings_interface, FIF.SETTING, '设置')

            logger.debug("界面初始化完成")

        except Exception as e:
            logger.error(f"界面初始化失败：{e}")

    def _create_home_interface(self):
        """创建主界面"""
        self.home_interface = QFrame(self)
        self.home_interface.setObjectName("home_interface")
        self.home_layout = QVBoxLayout(self.home_interface)

        # 顶部控制区域
        self._create_control_card()

        # 底部显示区域
        self._create_display_area()

    def _create_control_card(self):
        """创建控制卡片"""
        self.home_card = GroupHeaderCardWidget(self.home_interface)
        self.home_card.setTitle("基本设置")
        self.home_card.setBorderRadius(8)

        # 模型选择
        model_layout = QHBoxLayout()
        model_label = BodyLabel("检测模型", self.home_card)
        self.model_combo = ComboBox(self.home_card)
        self.model_combo.addItems(["蓝色", "橙色", "白色"])
        self.model_combo.setFixedWidth(320)

        model_layout.setSpacing(10)
        model_layout.setContentsMargins(24, 15, 24, 20)
        model_layout.addWidget(model_label, 0, Qt.AlignLeft)
        model_layout.addStretch(1)
        model_layout.addWidget(self.model_combo, 0, Qt.AlignRight)

        # 操作按钮
        operation_layout = QHBoxLayout()
        operation_label = BodyLabel("操作", self.home_card)
        self.take_img_btn = PrimaryPushButton("手动检测", self.home_card)
        self.init_process_btn = PushButton("初始化", self.home_card)

        operation_layout.setSpacing(10)
        operation_layout.setContentsMargins(24, 15, 24, 20)
        operation_layout.addWidget(operation_label, 0, Qt.AlignLeft)
        operation_layout.addStretch(1)
        operation_layout.addWidget(self.take_img_btn, 0, Qt.AlignRight)
        operation_layout.addWidget(self.init_process_btn, 0, Qt.AlignRight)

        self.home_card.vBoxLayout.addLayout(model_layout)
        self.home_card.vBoxLayout.addLayout(operation_layout)

        # 添加到主布局
        self.home_layout.addWidget(self.home_card, 1)

    def _create_display_area(self):
        """创建显示区域"""
        display_layout = QHBoxLayout()

        # 原图显示
        source_layout = QVBoxLayout()
        source_title = SubtitleLabel("原图", self.home_interface)
        self.source_image = ClickableImageLabel(self.home_interface)
        source_layout.addWidget(source_title, 1)
        source_layout.addWidget(self.source_image, 19)

        # 检测结果显示
        result_layout = QVBoxLayout()
        result_title = SubtitleLabel("检测结果", self.home_interface)
        self.result_image = ClickableImageLabel(self.home_interface)
        result_layout.addWidget(result_title, 1)
        result_layout.addWidget(self.result_image, 19)

        # 日志显示
        log_layout = QVBoxLayout()
        log_title = SubtitleLabel("系统日志", self.home_interface)
        self.log_text = TextBrowser(self.home_interface)
        self.log_text.setMaximumHeight(300)
        log_layout.addWidget(log_title, 1)
        log_layout.addWidget(self.log_text, 19)

        display_layout.addLayout(source_layout, 4)
        display_layout.addLayout(result_layout, 4)
        display_layout.addLayout(log_layout, 2)

        self.home_layout.addLayout(display_layout, 9)

    def _create_settings_interface(self):
        """创建设置界面"""
        self.settings_interface = QFrame(self)
        self.settings_interface.setObjectName("settings_interface")
        self.settings_layout = QVBoxLayout(self.settings_interface)

        # 检测设置卡片
        self._create_detection_settings_card()

        # 相机设置卡片
        self._create_camera_settings_card()

        # 保存按钮
        self.save_btn = PrimaryPushButton("保存设置", self.settings_interface)

        self.settings_layout.addWidget(self.detection_card, 2)
        self.settings_layout.addWidget(self.camera_card, 2)
        self.settings_layout.addStretch(6)
        self.settings_layout.addWidget(self.save_btn)

    def _create_detection_settings_card(self):
        """创建检测设置卡片"""
        self.detection_card = GroupHeaderCardWidget(self.settings_interface)
        self.detection_card.setTitle("检测设置")
        self.detection_card.setBorderRadius(8)

        # 黑点检测
        hd_layout = QHBoxLayout()
        hd_label = BodyLabel("黑点检测", self.detection_card)
        self.hd_combo = ComboBox(self.detection_card)
        self.hd_combo.addItems(["启用", "禁用"])
        self.hd_combo.setFixedWidth(320)

        hd_layout.setSpacing(10)
        hd_layout.setContentsMargins(24, 15, 24, 20)
        hd_layout.addWidget(hd_label, 0, Qt.AlignLeft)
        hd_layout.addStretch(1)
        hd_layout.addWidget(self.hd_combo, 0, Qt.AlignRight)

        # 凹坑检测
        ak_layout = QHBoxLayout()
        ak_label = BodyLabel("凹坑检测", self.detection_card)
        self.ak_combo = ComboBox(self.detection_card)
        self.ak_combo.addItems(["启用", "禁用"])
        self.ak_combo.setFixedWidth(320)

        ak_layout.setSpacing(10)
        ak_layout.setContentsMargins(24, 15, 24, 20)
        ak_layout.addWidget(ak_label, 0, Qt.AlignLeft)
        ak_layout.addStretch(1)
        ak_layout.addWidget(self.ak_combo, 0, Qt.AlignRight)

        # 划痕检测
        hh_layout = QHBoxLayout()
        hh_label = BodyLabel("划痕检测", self.detection_card)
        self.hh_combo = ComboBox(self.detection_card)
        self.hh_combo.addItems(["启用", "禁用"])
        self.hh_combo.setFixedWidth(320)

        hh_layout.setSpacing(10)
        hh_layout.setContentsMargins(24, 15, 24, 20)
        hh_layout.addWidget(hh_label, 0, Qt.AlignLeft)
        hh_layout.addStretch(1)
        hh_layout.addWidget(self.hh_combo, 0, Qt.AlignRight)

        self.detection_card.vBoxLayout.addLayout(hd_layout)
        self.detection_card.vBoxLayout.addLayout(ak_layout)
        self.detection_card.vBoxLayout.addLayout(hh_layout)

    def _create_camera_settings_card(self):
        """创建相机设置卡片"""
        self.camera_card = GroupHeaderCardWidget(self.settings_interface)
        self.camera_card.setTitle("相机参数")
        self.camera_card.setBorderRadius(8)

        # 曝光时间
        exposure_layout = QHBoxLayout()
        exposure_label = BodyLabel("曝光时间 (μs)", self.camera_card)
        self.exposure_input = DoubleSpinBox(self.camera_card)
        self.exposure_input.setRange(1.0, 1000000.0)
        self.exposure_input.setDecimals(1)
        self.exposure_input.setSuffix(" μs")

        exposure_layout.setSpacing(10)
        exposure_layout.setContentsMargins(24, 15, 24, 20)
        exposure_layout.addWidget(exposure_label, 0, Qt.AlignLeft)
        exposure_layout.addStretch(1)
        exposure_layout.addWidget(self.exposure_input, 0, Qt.AlignRight)

        # 增益
        gain_layout = QHBoxLayout()
        gain_label = BodyLabel("增益 (dB)", self.camera_card)
        self.gain_input = DoubleSpinBox(self.camera_card)
        self.gain_input.setRange(0.0, 40.0)
        self.gain_input.setDecimals(1)
        self.gain_input.setSuffix(" dB")

        gain_layout.setSpacing(10)
        gain_layout.setContentsMargins(24, 15, 24, 20)
        gain_layout.addWidget(gain_label, 0, Qt.AlignLeft)
        gain_layout.addStretch(1)
        gain_layout.addWidget(self.gain_input, 0, Qt.AlignRight)

        self.camera_card.vBoxLayout.addLayout(exposure_layout)
        self.camera_card.vBoxLayout.addLayout(gain_layout)

    @exception_handler(component="MainWindow", user_message="显示图像失败")
    def _on_image_captured(self, image: np.ndarray):
        """处理图像采集完成事件"""
        # 显示原图
        self.source_image.set_image(image)
        self.source_pixmap = self.source_image.get_pixmap()

    @exception_handler(component="MainWindow", user_message="显示检测结果失败")
    def _on_detection_completed(self, result: DetectionResult):
        """处理检测完成事件"""
        # 处理检测结果图像
        result_image = ImageProcessor.resize_image(result.original_image, 0.2)
        if result.draw_data:
            result_image = ImageProcessor.draw_detection_results(result_image, result.draw_data)

        # 转换颜色空间并显示
        if result_image is not None:
            display_image = ImageProcessor.convert_color_space(result_image, cv2.COLOR_BGR2RGB)
            self.result_image.set_image(display_image)
            self.result_pixmap = self.result_image.get_pixmap()

    def _on_system_status_changed(self, component: str, status: bool):
        """处理系统状态变化"""
        status_text = "正常" if status else "异常"
        logger.info(f"{component}状态：{status_text}")

    def _on_error_occurred(self, component: str, message: str):
        """处理错误事件"""
        logger.error(f"{component}错误：{message}")
        self._show_error_message(f"{component}错误", message)

    def _save_settings(self):
        """保存设置"""
        try:
            # 显示登录对话框
            login_dialog = LoginDialog(self.settings_interface)
            if not login_dialog.exec():
                return

            # 验证密码
            if login_dialog.get_password() != "123456":
                self._show_error_message("保存失败", "密码错误！")
                return

            # 获取设置值
            exposure = self.exposure_input.value()
            gain = self.gain_input.value()

            # 应用相机设置
            success = self.app_manager.set_camera_parameters(exposure, gain)
            if not success:
                self._show_error_message("保存失败", "相机参数设置失败")
                return

            # 记录设置信息
            hd_setting = self.hd_combo.currentText()
            ak_setting = self.ak_combo.currentText()
            hh_setting = self.hh_combo.currentText()

            logger.info(f"检测设置已更新 - 黑点检测: {hd_setting}, 凹坑检测: {ak_setting}, 划痕检测: {hh_setting}")
            logger.info(f"相机参数已更新 - 曝光: {exposure}μs, 增益: {gain}dB")

            # 显示成功消息
            self._show_success_message("保存成功", "设置已保存并应用")

        except Exception as e:
            logger.error(f"保存设置失败：{e}")
            self._show_error_message("保存失败", str(e))

    def _show_fullscreen_image(self, pixmap: Optional[QPixmap]):
        """显示全屏图像"""
        try:
            if pixmap and not pixmap.isNull():
                viewer = FullscreenImageViewer(pixmap, self)
                viewer.exec()
            else:
                logger.warning("没有可显示的图像")

        except Exception as e:
            logger.error(f"显示全屏图像失败：{e}")

    def _show_error_message(self, title: str, content: str):
        """显示错误消息"""
        try:
            InfoBar.error(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
        except Exception as e:
            logger.error(f"显示错误消息失败：{e}")

    def _show_success_message(self, title: str, content: str):
        """显示成功消息"""
        try:
            InfoBar.success(
                title=title,
                content=content,
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self
            )
        except Exception as e:
            logger.error(f"显示成功消息失败：{e}")

    def _load_camera_parameters(self):
        """加载相机参数到UI"""
        try:
            params = self.app_manager.get_camera_parameters()

            if params['exposure'] is not None:
                self.exposure_input.setValue(params['exposure'])
            if params['gain'] is not None:
                self.gain_input.setValue(params['gain'])

            logger.debug("相机参数已加载到UI")

        except Exception as e:
            logger.error(f"加载相机参数失败：{e}")

    def showEvent(self, event):
        """窗口显示事件"""
        super().showEvent(event)
        # 延迟加载相机参数
        QTimer.singleShot(2000, self._load_camera_parameters)

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 显示确认对话框
            reply = MessageBox(
                title="确认退出",
                content="确定要关闭视觉检测系统吗？",
                parent=self
            )

            if reply.exec():
                logger.info("用户确认退出系统")

                # 停止UI响应性管理
                self.ui_manager.stop_keep_alive()

                # 取消所有异步任务
                self.task_manager.cancel_all_tasks()

                # 停止应用程序管理器
                self.app_manager.stop_system()

                # 移除日志处理器
                if self.log_handler:
                    root_logger = logging.getLogger()
                    root_logger.removeHandler(self.log_handler)

                logger.info("视觉检测系统已关闭")
                event.accept()

            else:
                logger.info("用户取消退出")
                event.ignore()

        except Exception as e:
            logger.error(f"关闭程序时出错：{e}")
            event.accept()  # 即使出错也要关闭


def main():
    """主函数"""
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("兆威8150视觉检测系统")
        app.setApplicationVersion("2.0")

        # 设置全局异常处理
        global_exception_handler = setup_global_exception_handling(app)

        # 创建主窗口
        main_window = MainWindow()
        main_window.show()

        # 运行应用程序
        sys.exit(app.exec())

    except Exception as e:
        logger.error(f"应用程序启动失败：{e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
    