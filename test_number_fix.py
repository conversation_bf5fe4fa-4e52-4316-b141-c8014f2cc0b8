"""
测试数值转换修复
"""
import numpy as np

def ensure_number(value, number_type=float, default=0):
    """确保值是指定类型的数值"""
    try:
        if isinstance(value, (int, float)):
            return number_type(value)
        elif isinstance(value, (list, tuple)) and len(value) > 0:
            # 如果是列表或元组，取第一个元素
            return number_type(value[0])
        elif isinstance(value, str):
            return number_type(value)
        else:
            return number_type(default)
    except Exception:
        return number_type(default)

def test_ensure_number_function():
    """测试ensure_number函数"""
    print("=== 测试ensure_number函数 ===")
    
    # 测试各种输入
    test_cases = [
        # (输入值, 类型, 默认值, 期望结果, 描述)
        (1.5, float, 0.0, 1.5, "正常浮点数"),
        (10, int, 0, 10, "正常整数"),
        ([2.5, 3.0], float, 0.0, 2.5, "浮点数列表取第一个"),
        ((5, 6), int, 0, 5, "整数元组取第一个"),
        ("3.14", float, 0.0, 3.14, "字符串转浮点数"),
        ("42", int, 0, 42, "字符串转整数"),
        ([], float, 1.0, 1.0, "空列表使用默认值"),
        (None, float, 2.0, 2.0, "None使用默认值"),
        ("invalid", float, 3.0, 3.0, "无效字符串使用默认值"),
        ([1.5, 2.5, 3.5], float, 0.0, 1.5, "多元素列表取第一个"),
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for input_val, num_type, default, expected, desc in test_cases:
        try:
            result = ensure_number(input_val, num_type, default)
            if result == expected:
                print(f"✅ {desc}: {input_val} -> {result}")
                success_count += 1
            else:
                print(f"❌ {desc}: {input_val} -> {result}, 期望: {expected}")
        except Exception as e:
            print(f"❌ {desc}: 异常 {e}")
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 通过")
    return success_count == total_count

def test_problematic_scenarios():
    """测试会导致原始错误的场景"""
    print("\n=== 测试原始错误场景 ===")
    
    # 模拟会导致 float() argument must be a string or a real number, not 'tuple' 的情况
    problematic_data = [
        # 这些数据可能导致原始错误
        ['putText', 'Hello', [50, 50], 0, (1.0, 2.0), [255, 255, 255], (2, 3)],  # scale和thickness是元组
        ['circle', [100, 100], [20, 25], [0, 255, 0], (2, 3)],  # radius和thickness是列表/元组
        ['rectangle', [150, 150], [250, 200], [255, 0, 0], [2, 3]],  # thickness是列表
    ]
    
    success_count = 0
    total_count = len(problematic_data)
    
    for item in problematic_data:
        try:
            command = item[0]
            print(f"\n🔧 处理命令: {command}")
            
            if command == 'putText' and len(item) >= 6:
                scale_val = item[4] if len(item) > 4 else 1.0
                thickness_val = item[6] if len(item) > 6 else 2
                
                print(f"  原始 scale: {scale_val} (类型: {type(scale_val)})")
                print(f"  原始 thickness: {thickness_val} (类型: {type(thickness_val)})")
                
                # 使用修复后的方法
                scale = ensure_number(scale_val, float, 1.0)
                thickness = ensure_number(thickness_val, int, 2)
                
                print(f"  修复后 scale: {scale} (类型: {type(scale)})")
                print(f"  修复后 thickness: {thickness} (类型: {type(thickness)})")
                
                if isinstance(scale, float) and isinstance(thickness, int):
                    print(f"✅ putText 数值转换成功")
                    success_count += 1
                else:
                    print(f"❌ putText 数值转换失败")
                    
            elif command == 'circle' and len(item) >= 5:
                radius_val = item[2]
                thickness_val = item[4] if len(item) > 4 else 2
                
                print(f"  原始 radius: {radius_val} (类型: {type(radius_val)})")
                print(f"  原始 thickness: {thickness_val} (类型: {type(thickness_val)})")
                
                # 使用修复后的方法
                radius = ensure_number(radius_val, int, 10)
                thickness = ensure_number(thickness_val, int, 2)
                
                print(f"  修复后 radius: {radius} (类型: {type(radius)})")
                print(f"  修复后 thickness: {thickness} (类型: {type(thickness)})")
                
                if isinstance(radius, int) and isinstance(thickness, int):
                    print(f"✅ circle 数值转换成功")
                    success_count += 1
                else:
                    print(f"❌ circle 数值转换失败")
                    
            elif command == 'rectangle' and len(item) >= 5:
                thickness_val = item[4] if len(item) > 4 else 2
                
                print(f"  原始 thickness: {thickness_val} (类型: {type(thickness_val)})")
                
                # 使用修复后的方法
                thickness = ensure_number(thickness_val, int, 2)
                
                print(f"  修复后 thickness: {thickness} (类型: {type(thickness)})")
                
                if isinstance(thickness, int):
                    print(f"✅ rectangle 数值转换成功")
                    success_count += 1
                else:
                    print(f"❌ rectangle 数值转换失败")
                    
        except Exception as e:
            print(f"❌ 处理 {command} 时出错: {e}")
    
    print(f"\n📊 问题场景处理结果: {success_count}/{total_count} 成功")
    return success_count == total_count

def test_original_error_simulation():
    """模拟原始错误"""
    print("\n=== 模拟原始错误 ===")
    
    # 模拟会导致错误的代码
    problematic_values = [
        ((1.0, 2.0), "元组作为scale"),
        ([2, 3], "列表作为thickness"),
        (None, "None值"),
        ("invalid", "无效字符串"),
    ]
    
    for value, desc in problematic_values:
        print(f"\n🔧 测试: {desc}")
        print(f"原始值: {value} (类型: {type(value)})")
        
        try:
            # 这是原来会出错的代码
            # result = float(value)  # 这会出错
            
            # 现在的修复代码
            result_float = ensure_number(value, float, 1.0)
            result_int = ensure_number(value, int, 2)
            
            print(f"修复后 float: {result_float}")
            print(f"修复后 int: {result_int}")
            print(f"✅ {desc} 处理成功")
            
        except Exception as e:
            print(f"❌ {desc} 处理失败: {e}")
            return False
    
    return True

def main():
    """主测试函数"""
    print("=== 数值转换修复测试 ===\n")
    
    test1 = test_ensure_number_function()
    test2 = test_problematic_scenarios()
    test3 = test_original_error_simulation()
    
    print(f"\n=== 测试总结 ===")
    
    if test1 and test2 and test3:
        print("🎉 所有测试通过！")
        print("✅ ensure_number函数工作正常")
        print("✅ 问题场景处理正确")
        print("✅ 原始错误已修复")
        print("\n📋 修复内容:")
        print("1. 添加了_ensure_number辅助函数")
        print("2. 安全处理各种数据类型的数值转换")
        print("3. 元组和列表会取第一个元素")
        print("4. 无效数据会使用默认值")
        print("5. 所有数值参数都经过安全转换")
        print("\n🚀 现在不会再出现 float() argument must be a string or a real number, not 'tuple' 错误了！")
    else:
        print("❌ 部分测试失败")
        if not test1:
            print("  - ensure_number函数需要进一步修复")
        if not test2:
            print("  - 问题场景处理需要进一步修复")
        if not test3:
            print("  - 原始错误修复不完整")

if __name__ == "__main__":
    main()
