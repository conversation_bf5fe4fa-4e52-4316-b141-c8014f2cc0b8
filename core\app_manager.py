"""
应用程序管理器
负责协调各个模块之间的交互，管理信号连接
"""
import os
import logging
from datetime import datetime
from typing import Optional, Dict, Any
import cv2
import numpy as np
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import QApplication

from .camera_controller import CameraController
from .modbus_controller import ModbusManager
from .image_detector import DetectionManager, DetectionResult
from .resource_manager import get_resource_manager, FileManager, MemoryMonitor

logger = logging.getLogger(__name__)


class ApplicationManager(QObject):
    """应用程序管理器，协调各个模块"""
    
    # 信号定义
    image_captured = pyqtSignal(np.ndarray)  # 图像采集完成
    detection_completed = pyqtSignal(DetectionResult)  # 检测完成
    system_status_changed = pyqtSignal(str, bool)  # 系统状态变化 (component, status)
    error_occurred = pyqtSignal(str, str)  # 错误发生 (component, message)
    log_message = pyqtSignal(str, str)  # 日志消息 (level, message)
    
    def __init__(self):
        super().__init__()

        # 资源管理
        self.resource_manager = get_resource_manager()
        self.file_manager = FileManager(self.resource_manager)
        self.memory_monitor = MemoryMonitor(self.resource_manager)

        # 核心组件
        self.camera_controller: Optional[CameraController] = None
        self.modbus_manager: Optional[ModbusManager] = None
        self.detection_manager: Optional[DetectionManager] = None
        
        # 配置参数
        self.config = {
            'modbus_host': '************',
            'modbus_port': 502,
            'detection_api_url': 'http://**********:30304/image',
            'detection_timeout': 5,
            'image_save_path': 'D://images',
            'model_map': {0: "蓝色", 1: "橙色", 2: "白色"}
        }
        
        # 状态管理
        self.is_initialized = False
        self.current_model_index = 0
        
        # 统计信息
        self.stats = {
            'total_detections': 0,
            'successful_detections': 0,
            'failed_detections': 0,
            'last_detection_time': None
        }
        
        # 初始化组件
        self._initialize_components()
        
    def _initialize_components(self):
        """初始化各个组件"""
        try:
            # 创建相机控制器
            self.camera_controller = CameraController()
            self.resource_manager.register_resource(
                "camera_controller",
                self.camera_controller,
                "camera",
                lambda: self.camera_controller.close() if self.camera_controller else None
            )
            self._connect_camera_signals()

            # 创建Modbus管理器
            self.modbus_manager = ModbusManager(
                self.config['modbus_host'],
                self.config['modbus_port']
            )
            self.resource_manager.register_resource(
                "modbus_manager",
                self.modbus_manager,
                "modbus",
                lambda: self.modbus_manager.stop() if self.modbus_manager else None
            )
            self._connect_modbus_signals()

            # 创建检测管理器
            self.detection_manager = DetectionManager(
                self.config['detection_api_url'],
                self.config['detection_timeout']
            )
            self.resource_manager.register_resource(
                "detection_manager",
                self.detection_manager,
                "detection",
                lambda: self.detection_manager.stop() if self.detection_manager else None
            )
            self._connect_detection_signals()

            logger.info("应用程序管理器初始化完成")

        except Exception as e:
            error_msg = f"组件初始化失败：{e}"
            logger.error(error_msg)
            self.error_occurred.emit("AppManager", error_msg)
            
    def _connect_camera_signals(self):
        """连接相机信号"""
        if self.camera_controller:
            self.camera_controller.image_captured.connect(self._on_image_captured)
            self.camera_controller.camera_error.connect(
                lambda msg: self.error_occurred.emit("Camera", msg)
            )
            self.camera_controller.camera_status_changed.connect(
                lambda status: self.system_status_changed.emit("Camera", status)
            )
            
    def _connect_modbus_signals(self):
        """连接Modbus信号"""
        if self.modbus_manager:
            self.modbus_manager.trigger_received.connect(self._on_trigger_received)
            self.modbus_manager.modbus_error.connect(
                lambda msg: self.error_occurred.emit("Modbus", msg)
            )
            self.modbus_manager.connection_status_changed.connect(
                lambda status: self.system_status_changed.emit("Modbus", status)
            )
            
    def _connect_detection_signals(self):
        """连接检测信号"""
        if self.detection_manager:
            self.detection_manager.detection_completed.connect(self._on_detection_completed)
            self.detection_manager.detection_error.connect(
                lambda task_id, msg: self.error_occurred.emit("Detection", f"任务{task_id}: {msg}")
            )
            
    def start_system(self) -> bool:
        """启动系统"""
        try:
            logger.info("正在启动视觉检测系统...")
            
            # 启动相机
            if not self.camera_controller.initialize():
                raise RuntimeError("相机初始化失败")
                
            # 启动Modbus通信
            self.modbus_manager.start()
            
            # 启动检测服务
            self.detection_manager.start()
            
            # 确保图像保存目录存在
            os.makedirs(self.config['image_save_path'], exist_ok=True)

            # 启动内存监控
            self.memory_monitor.start_monitoring()

            self.is_initialized = True
            logger.info("视觉检测系统启动成功")
            return True
            
        except Exception as e:
            error_msg = f"系统启动失败：{e}"
            logger.error(error_msg)
            self.error_occurred.emit("System", error_msg)
            return False
            
    def stop_system(self):
        """停止系统"""
        try:
            logger.info("正在停止视觉检测系统...")

            # 停止内存监控
            self.memory_monitor.stop_monitoring()

            # 清理所有资源
            self.resource_manager.cleanup_all()

            # 清理临时文件
            self.file_manager.cleanup_temp_files()
            self.file_manager.cleanup_temp_dirs()

            self.is_initialized = False
            logger.info("视觉检测系统已停止")

        except Exception as e:
            logger.error(f"停止系统时出错：{e}")
            
    def _on_trigger_received(self):
        """处理触发信号"""
        try:
            if not self.is_initialized:
                logger.warning("系统未初始化，忽略触发信号")
                return
                
            model_name = self.config['model_map'].get(self.current_model_index, "未知")
            logger.info(f"收到触发信号，当前模型：{model_name}")
            
            # 发送软件触发
            if self.camera_controller:
                self.camera_controller.send_software_trigger()
                
        except Exception as e:
            logger.error(f"处理触发信号失败：{e}")
            
    def _on_image_captured(self, image: np.ndarray):
        """处理图像采集完成"""
        try:
            logger.debug("图像采集完成，开始检测")
            
            # 发送图像给UI显示
            self.image_captured.emit(image)
            
            # 提交检测任务
            if self.detection_manager:
                task_id = self.detection_manager.submit_detection_task(
                    image, self.current_model_index
                )
                logger.debug(f"检测任务已提交：{task_id}")
                
            # 重置Modbus检测状态
            if self.modbus_manager:
                self.modbus_manager.reset_checking_state()
                
        except Exception as e:
            logger.error(f"处理图像采集失败：{e}")
            
    def _on_detection_completed(self, result: DetectionResult):
        """处理检测完成"""
        try:
            # 更新统计信息
            self.stats['total_detections'] += 1
            self.stats['last_detection_time'] = datetime.now()
            
            if result.result_status == 'OK':
                self.stats['successful_detections'] += 1
                reply_value = 2  # 合格
                logger.info(f"检测结果：合格 - 耗时：{result.processing_time:.2f}s")
            else:
                self.stats['failed_detections'] += 1
                reply_value = 4  # 不合格
                logger.info(f"检测结果：不合格 - 耗时：{result.processing_time:.2f}s")
                
            # 添加Modbus回复值
            if self.modbus_manager:
                self.modbus_manager.add_reply_value(reply_value)
                
            # 保存图像
            self._save_detection_images(result)
            
            # 发送检测结果给UI
            self.detection_completed.emit(result)
            
        except Exception as e:
            logger.error(f"处理检测结果失败：{e}")
            
    def _save_detection_images(self, result: DetectionResult):
        """保存检测图像"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            
            # 保存原图
            source_path = os.path.join(
                self.config['image_save_path'], 
                f"source_{timestamp}.bmp"
            )
            cv2.imwrite(source_path, result.original_image)
            
            # 保存检测结果图
            if result.draw_data:
                from ui.components import ImageProcessor
                result_image = ImageProcessor.draw_detection_results(
                    result.original_image, result.draw_data
                )
                result_path = os.path.join(
                    self.config['image_save_path'],
                    f"result_{timestamp}.bmp"
                )
                cv2.imwrite(result_path, result_image)
                
            logger.debug(f"检测图像已保存：{timestamp}")
            
        except Exception as e:
            logger.error(f"保存检测图像失败：{e}")
            
    def manual_trigger(self):
        """手动触发检测"""
        try:
            if not self.is_initialized:
                logger.warning("系统未初始化，无法手动触发")
                return False
                
            logger.info("手动触发检测")
            if self.camera_controller:
                return self.camera_controller.send_software_trigger()
            return False
            
        except Exception as e:
            logger.error(f"手动触发失败：{e}")
            return False
            
    def set_model_index(self, index: int):
        """设置当前模型索引"""
        if index in self.config['model_map']:
            self.current_model_index = index
            model_name = self.config['model_map'][index]
            logger.info(f"当前模型已切换为：{model_name}")
        else:
            logger.warning(f"无效的模型索引：{index}")
            
    def set_camera_parameters(self, exposure: float, gain: float) -> bool:
        """设置相机参数"""
        try:
            if not self.camera_controller or not self.camera_controller.is_initialized:
                logger.warning("相机未初始化，无法设置参数")
                return False
                
            success = True
            if not self.camera_controller.set_exposure(exposure):
                success = False
            if not self.camera_controller.set_gain(gain):
                success = False
                
            return success
            
        except Exception as e:
            logger.error(f"设置相机参数失败：{e}")
            return False
            
    def get_camera_parameters(self) -> Dict[str, Optional[float]]:
        """获取相机参数"""
        try:
            if not self.camera_controller or not self.camera_controller.is_initialized:
                return {'exposure': None, 'gain': None}
                
            return {
                'exposure': self.camera_controller.get_exposure(),
                'gain': self.camera_controller.get_gain()
            }
            
        except Exception as e:
            logger.error(f"获取相机参数失败：{e}")
            return {'exposure': None, 'gain': None}
            
    def clear_detection_queue(self):
        """清空检测队列"""
        if self.detection_manager:
            self.detection_manager.clear_queue()
            logger.info("检测队列已清空")
            
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'initialized': self.is_initialized,
            'camera_ready': self.camera_controller and self.camera_controller.is_initialized,
            'modbus_connected': self.modbus_manager and hasattr(self.modbus_manager, 'controller') and self.modbus_manager.controller.is_connected,
            'detection_queue_size': self.detection_manager.get_queue_size() if self.detection_manager else 0,
            'current_model': self.config['model_map'].get(self.current_model_index, "未知"),
            'stats': self.stats.copy()
        }
        
    def update_config(self, config_updates: Dict[str, Any]):
        """更新配置"""
        try:
            self.config.update(config_updates)
            logger.info(f"配置已更新：{config_updates}")
            
            # 如果更新了检测API配置，应用到检测管理器
            if 'detection_api_url' in config_updates or 'detection_timeout' in config_updates:
                if self.detection_manager:
                    self.detection_manager.set_api_config(
                        self.config['detection_api_url'],
                        self.config['detection_timeout']
                    )
                    
        except Exception as e:
            logger.error(f"更新配置失败：{e}")
