"""
图像检测模块
负责图像检测任务的管理和处理
"""
import ast
import time
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from queue import Queue, Empty
import cv2
import numpy as np
import requests
from PyQt5.QtCore import QObject, QThread, pyqtSignal, QMutex, QMutexLocker

logger = logging.getLogger(__name__)


@dataclass
class DetectionTask:
    """检测任务数据类"""
    image: np.ndarray
    model_description: str
    timestamp: float
    task_id: str


@dataclass
class DetectionResult:
    """检测结果数据类"""
    task_id: str
    original_image: np.ndarray
    result_status: str
    draw_data: List[Any]
    processing_time: float


class ImageDetector(QObject):
    """图像检测器"""
    
    # 信号定义
    detection_completed = pyqtSignal(DetectionResult)  # 检测完成信号
    detection_error = pyqtSignal(str, str)  # 检测错误信号 (task_id, error_msg)
    queue_size_changed = pyqtSignal(int)  # 队列大小变化信号
    
    def __init__(self, api_url: str = 'http://**********:30304/image', timeout: int = 5):
        super().__init__()
        self.api_url = api_url
        self.timeout = timeout
        self.model_map = {
            "0": "blue",
            "1": "orange", 
            "2": "white"
        }
        
    def detect_image(self, image: np.ndarray, model_description: str, task_id: str) -> Optional[DetectionResult]:
        """
        检测单张图像
        Args:
            image: 输入图像
            model_description: 模型描述
            task_id: 任务ID
        Returns:
            Optional[DetectionResult]: 检测结果，失败返回None
        """
        start_time = time.time()
        
        try:
            # 转换图像格式
            img_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            
            # 编码图像
            success, img_encoded = cv2.imencode('.bmp', img_bgr)
            if not success:
                raise ValueError("图像编码失败")
                
            # 准备请求数据
            files = {'file': ('image.bmp', img_encoded.tobytes(), 'application/octet-stream')}
            data = {'description': model_description}
            
            # 发送请求
            response = requests.post(
                self.api_url, 
                files=files, 
                data=data, 
                timeout=self.timeout
            )
            response.raise_for_status()
            
            # 解析响应
            response_parts = response.text.split("--")
            if len(response_parts) != 3:
                raise ValueError(f"响应格式错误：{response.text}")
                
            _, result_status, draw_data_str = response_parts
            
            # 解析绘制数据
            try:
                draw_data = ast.literal_eval(draw_data_str)
            except (ValueError, SyntaxError) as e:
                logger.warning(f"绘制数据解析失败：{e}")
                draw_data = []
                
            processing_time = time.time() - start_time
            
            # 创建检测结果
            result = DetectionResult(
                task_id=task_id,
                original_image=img_bgr,
                result_status=result_status.strip(),
                draw_data=draw_data,
                processing_time=processing_time
            )
            
            logger.info(f"检测完成 - 任务ID: {task_id}, 结果: {result_status}, 耗时: {processing_time:.2f}s")
            return result
            
        except requests.exceptions.Timeout:
            error_msg = f"检测请求超时（{self.timeout}s）"
            logger.error(f"任务{task_id} - {error_msg}")
            self.detection_error.emit(task_id, error_msg)
            
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求失败：{e}"
            logger.error(f"任务{task_id} - {error_msg}")
            self.detection_error.emit(task_id, error_msg)
            
        except Exception as e:
            error_msg = f"检测异常：{e}"
            logger.error(f"任务{task_id} - {error_msg}")
            self.detection_error.emit(task_id, error_msg)
            
        return None


class DetectionWorkerThread(QThread):
    """检测工作线程"""
    
    def __init__(self, detector: ImageDetector):
        super().__init__()
        self.detector = detector
        self.task_queue = Queue()
        self._running = True
        self._mutex = QMutex()
        
    def add_task(self, task: DetectionTask):
        """
        添加检测任务
        Args:
            task: 检测任务
        """
        with QMutexLocker(self._mutex):
            self.task_queue.put(task)
            self.detector.queue_size_changed.emit(self.task_queue.qsize())
            logger.debug(f"添加检测任务：{task.task_id}，队列大小：{self.task_queue.qsize()}")
            
    def get_queue_size(self) -> int:
        """获取队列大小"""
        with QMutexLocker(self._mutex):
            return self.task_queue.qsize()
            
    def clear_queue(self):
        """清空任务队列"""
        with QMutexLocker(self._mutex):
            while not self.task_queue.empty():
                try:
                    self.task_queue.get_nowait()
                except Empty:
                    break
            self.detector.queue_size_changed.emit(0)
            logger.info("检测任务队列已清空")
            
    def run(self):
        """线程主循环"""
        logger.info("检测工作线程已启动")
        
        while self._running:
            try:
                # 获取任务（带超时）
                try:
                    task = self.task_queue.get(timeout=0.1)
                except Empty:
                    continue
                    
                # 更新队列大小
                self.detector.queue_size_changed.emit(self.task_queue.qsize())
                
                # 执行检测
                result = self.detector.detect_image(
                    task.image, 
                    task.model_description, 
                    task.task_id
                )
                
                # 发送结果
                if result:
                    self.detector.detection_completed.emit(result)
                    
                # 标记任务完成
                self.task_queue.task_done()
                
            except Exception as e:
                logger.error(f"检测工作线程异常：{e}")
                
        logger.info("检测工作线程已停止")
        
    def stop(self):
        """停止线程"""
        logger.info("正在停止检测工作线程...")
        self._running = False


class DetectionManager(QObject):
    """检测管理器，提供高级接口"""
    
    # 信号定义
    detection_completed = pyqtSignal(DetectionResult)
    detection_error = pyqtSignal(str, str)
    queue_size_changed = pyqtSignal(int)
    
    def __init__(self, api_url: str = 'http://**********:30304/image', timeout: int = 5):
        super().__init__()
        self.detector = ImageDetector(api_url, timeout)
        self.worker_thread: Optional[DetectionWorkerThread] = None
        self._task_counter = 0
        
        # 连接信号
        self.detector.detection_completed.connect(self.detection_completed)
        self.detector.detection_error.connect(self.detection_error)
        self.detector.queue_size_changed.connect(self.queue_size_changed)
        
    def start(self):
        """启动检测服务"""
        if not self.worker_thread or not self.worker_thread.isRunning():
            logger.info("启动图像检测服务...")
            self.worker_thread = DetectionWorkerThread(self.detector)
            self.worker_thread.start()
            
    def stop(self):
        """停止检测服务"""
        if self.worker_thread and self.worker_thread.isRunning():
            logger.info("停止图像检测服务...")
            self.worker_thread.stop()
            self.worker_thread.wait(3000)  # 等待最多3秒
            
    def submit_detection_task(self, image: np.ndarray, model_index: int) -> str:
        """
        提交检测任务
        Args:
            image: 输入图像
            model_index: 模型索引
        Returns:
            str: 任务ID
        """
        if not self.worker_thread or not self.worker_thread.isRunning():
            raise RuntimeError("检测服务未启动")
            
        # 生成任务ID
        self._task_counter += 1
        task_id = f"task_{self._task_counter}_{int(time.time() * 1000)}"
        
        # 获取模型描述
        model_description = self.detector.model_map.get(str(model_index), "unknown")
        
        # 创建任务
        task = DetectionTask(
            image=image.copy(),  # 复制图像避免数据竞争
            model_description=model_description,
            timestamp=time.time(),
            task_id=task_id
        )
        
        # 添加到队列
        self.worker_thread.add_task(task)
        
        logger.info(f"提交检测任务：{task_id}，模型：{model_description}")
        return task_id
        
    def get_queue_size(self) -> int:
        """获取当前队列大小"""
        if self.worker_thread:
            return self.worker_thread.get_queue_size()
        return 0
        
    def clear_queue(self):
        """清空任务队列"""
        if self.worker_thread:
            self.worker_thread.clear_queue()
            
    def set_api_config(self, api_url: str, timeout: int):
        """
        设置API配置
        Args:
            api_url: API地址
            timeout: 超时时间
        """
        self.detector.api_url = api_url
        self.detector.timeout = timeout
        logger.info(f"API配置已更新 - URL: {api_url}, 超时: {timeout}s")
