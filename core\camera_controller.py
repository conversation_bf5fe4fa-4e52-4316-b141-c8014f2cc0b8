"""
相机控制器模块
负责相机的初始化、配置和图像采集
"""
import logging
from typing import Optional, Callable
from contextlib import contextmanager
from ctypes import c_ubyte, addressof
import numpy as np
import gxipy as gx
from PyQt5.QtCore import QObject, pyqtSignal

logger = logging.getLogger(__name__)


class CameraController(QObject):
    """相机控制器类，负责相机的所有操作"""
    
    # 信号定义
    image_captured = pyqtSignal(np.ndarray)  # 图像采集完成信号
    camera_error = pyqtSignal(str)  # 相机错误信号
    camera_status_changed = pyqtSignal(bool)  # 相机状态变化信号
    
    def __init__(self):
        super().__init__()
        self.device_manager: Optional[gx.DeviceManager] = None
        self.cam: Optional[gx.Device] = None
        self.remote_device_feature = None
        self.image_process_config = None
        self.image_format_convert = None
        self.image_process = None
        self._is_initialized = False
        self._capture_callback: Optional[Callable] = None
        
    @property
    def is_initialized(self) -> bool:
        """检查相机是否已初始化"""
        return self._is_initialized
        
    def initialize(self) -> bool:
        """
        初始化相机
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("开始初始化相机...")
            
            # 创建设备管理器
            self.device_manager = gx.DeviceManager()
            
            # 更新设备列表
            dev_num, dev_info_list = self.device_manager.update_all_device_list()
            if dev_num == 0:
                error_msg = "未找到相机设备"
                logger.error(error_msg)
                self.camera_error.emit(error_msg)
                return False
                
            # 打开第一个设备
            sn = dev_info_list[0].get("sn")
            logger.info(f"找到相机设备，序列号：{sn}")
            self.cam = self.device_manager.open_device_by_sn(sn)
            
            # 获取设备特性控制器
            self.remote_device_feature = self.cam.get_remote_device_feature_control()
            
            # 创建图像处理相关对象
            self.image_process_config = self.cam.create_image_process_config()
            self.image_format_convert = self.device_manager.create_image_format_convert()
            self.image_process = self.device_manager.create_image_process()
            
            # 设置触发模式
            self.set_trigger_mode("On")
            self.set_trigger_source("Software")
            
            # 注册回调函数
            self._capture_callback = self._on_image_captured
            self.cam.data_stream[0].register_capture_callback(self._capture_callback)
            
            # 开始采集
            self.cam.stream_on()
            
            self._is_initialized = True
            self.camera_status_changed.emit(True)
            logger.info("相机初始化成功，开始采集")
            return True
            
        except Exception as e:
            error_msg = f"相机初始化失败：{e}"
            logger.error(error_msg)
            self.camera_error.emit(error_msg)
            self._cleanup_resources()
            return False
            
    def _on_image_captured(self, raw_image):
        """
        图像采集回调函数
        Args:
            raw_image: 原始图像数据
        """
        try:
            # 处理图像数据
            numpy_img = self._process_raw_image(raw_image)
            if numpy_img is not None:
                self.image_captured.emit(numpy_img)
        except Exception as e:
            error_msg = f"图像处理失败：{e}"
            logger.error(error_msg)
            self.camera_error.emit(error_msg)
            
    def _process_raw_image(self, raw_image) -> Optional[np.ndarray]:
        """
        处理原始图像数据
        Args:
            raw_image: 原始图像数据
        Returns:
            np.ndarray: 处理后的numpy图像数组
        """
        try:
            pixel_format = raw_image.frame_data.pixel_format
            width = raw_image.frame_data.width
            height = raw_image.frame_data.height
            
            # 根据像素格式分配缓冲区
            if pixel_format in [gx.GxPixelFormatEntry.MONO8]:
                buffer = (c_ubyte * (width * height))()
            else:
                buffer = (c_ubyte * (width * height * 3))()
                
            # 设置图像处理配置
            self.image_process_config.set_valid_bits(gx.DxValidBit.BIT0_7)
            self.image_process.image_improvement(raw_image, addressof(buffer), self.image_process_config)
            
            # 创建RGB图像数组
            if gx.Utility.is_gray(raw_image.frame_data.pixel_format):
                rgb_image_array = (c_ubyte * height * width)()
            else:
                rgb_image_array = (c_ubyte * height * width * 3)()
                
            rgb_image_array_address = addressof(rgb_image_array)
            self.image_process.image_improvement(raw_image, rgb_image_array_address, self.image_process_config)
            
            # 转换为numpy数组
            numpy_img = np.frombuffer(
                rgb_image_array, 
                dtype=np.ubyte,
                count=width * height * 3
            ).reshape(height, width, 3)
            
            return numpy_img
            
        except Exception as e:
            logger.error(f"原始图像处理失败：{e}")
            return None
            
    def set_trigger_mode(self, mode_str: str = "On") -> bool:
        """
        设置触发模式
        Args:
            mode_str: 触发模式字符串
        Returns:
            bool: 设置是否成功
        """
        try:
            if (self.remote_device_feature and 
                self.remote_device_feature.is_implemented("TriggerMode")):
                self.remote_device_feature.get_enum_feature("TriggerMode").set(mode_str)
                logger.info(f"触发模式设置为：{mode_str}")
                return True
        except Exception as e:
            logger.error(f"设置触发模式失败：{e}")
        return False
        
    def set_trigger_source(self, source_str: str = "Software") -> bool:
        """
        设置触发源
        Args:
            source_str: 触发源字符串
        Returns:
            bool: 设置是否成功
        """
        try:
            if (self.remote_device_feature and 
                self.remote_device_feature.is_implemented("TriggerSource")):
                self.remote_device_feature.get_enum_feature("TriggerSource").set(source_str)
                logger.info(f"触发源设置为：{source_str}")
                return True
        except Exception as e:
            logger.error(f"设置触发源失败：{e}")
        return False
        
    def send_software_trigger(self) -> bool:
        """
        发送软件触发
        Returns:
            bool: 触发是否成功
        """
        try:
            if self.remote_device_feature:
                self.remote_device_feature.get_command_feature("TriggerSoftware").send_command()
                logger.debug("软件触发已发送")
                return True
        except Exception as e:
            logger.error(f"发送软件触发失败：{e}")
        return False
        
    def set_exposure(self, value: float) -> bool:
        """
        设置曝光时间
        Args:
            value: 曝光时间值
        Returns:
            bool: 设置是否成功
        """
        try:
            if (self.remote_device_feature and 
                self.remote_device_feature.is_implemented("ExposureTime")):
                self.remote_device_feature.get_float_feature("ExposureTime").set(value)
                logger.info(f"曝光时间设置为：{value}")
                return True
        except Exception as e:
            logger.error(f"设置曝光时间失败：{e}")
        return False
        
    def set_gain(self, value: float) -> bool:
        """
        设置增益
        Args:
            value: 增益值
        Returns:
            bool: 设置是否成功
        """
        try:
            if (self.remote_device_feature and 
                self.remote_device_feature.is_implemented("Gain")):
                self.remote_device_feature.get_float_feature("Gain").set(value)
                logger.info(f"增益设置为：{value}")
                return True
        except Exception as e:
            logger.error(f"设置增益失败：{e}")
        return False
        
    def get_exposure(self) -> Optional[float]:
        """获取当前曝光时间"""
        try:
            if (self.remote_device_feature and 
                self.remote_device_feature.is_implemented("ExposureTime")):
                return self.remote_device_feature.get_float_feature("ExposureTime").get()
        except Exception as e:
            logger.error(f"获取曝光时间失败：{e}")
        return None
        
    def get_gain(self) -> Optional[float]:
        """获取当前增益"""
        try:
            if (self.remote_device_feature and 
                self.remote_device_feature.is_implemented("Gain")):
                return self.remote_device_feature.get_float_feature("Gain").get()
        except Exception as e:
            logger.error(f"获取增益失败：{e}")
        return None
        
    def _cleanup_resources(self):
        """清理资源"""
        try:
            if self.cam:
                if hasattr(self.cam, 'stream_off'):
                    self.cam.stream_off()
                if hasattr(self.cam, 'close_device'):
                    self.cam.close_device()
                self.cam = None
                
            self.remote_device_feature = None
            self.image_process_config = None
            self.image_format_convert = None
            self.image_process = None
            self._capture_callback = None
            
        except Exception as e:
            logger.error(f"清理相机资源时出错：{e}")
            
    def close(self):
        """关闭相机并清理资源"""
        if self._is_initialized:
            logger.info("正在关闭相机...")
            self._cleanup_resources()
            self._is_initialized = False
            self.camera_status_changed.emit(False)
            logger.info("相机已关闭")
            
    @contextmanager
    def camera_context(self):
        """相机上下文管理器"""
        try:
            if self.initialize():
                yield self
            else:
                raise RuntimeError("相机初始化失败")
        finally:
            self.close()
